// <PERSON>ript to reset drafts localStorage for debugging
// Run this in browser console to clear all drafts and start fresh

console.log('Current drafts:', localStorage.getItem('event_drafts'));

// Clear all drafts
localStorage.removeItem('event_drafts');

console.log('Drafts cleared. localStorage is now clean.');

// Create a single test draft to verify functionality
const testDraft = {
  id: 'draft_test_123',
  title: 'Test Event',
  description: 'This is a test event to verify the draft system works correctly.',
  date: '2024-02-15',
  time: '14:00',
  location: 'Test Location',
  category: 'Academic',
  organizer: 'Test Organizer',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

const drafts = {};
drafts[testDraft.id] = testDraft;
localStorage.setItem('event_drafts', JSON.stringify(drafts));

console.log('Test draft created:', testDraft);
console.log('New localStorage state:', localStorage.getItem('event_drafts'));
