"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Menu, Bell, Search, PanelLeftClose, PanelLeft, Filter, X, Command } from "lucide-react"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"

interface TopBarProps {
  title?: string
  onMenuClick?: () => void
  sidebarCollapsed?: boolean
}

export function TopBar({ title = "UniVibe", onMenuClick, sidebarCollapsed }: TopBarProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [isSearchFocused, setIsSearchFocused] = useState(false)
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const router = useRouter()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Navigate to home page with search query (you can implement search functionality)
      router.push(`/?search=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  const clearSearch = () => {
    setSearchQuery("")
    setIsSearchExpanded(false)
  }

  return (
    <header className="sticky top-0 z-40 bg-card/95 backdrop-blur-sm border-b border-white/10">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-3">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onMenuClick}
            className="lg:hidden hover:bg-white/10 transition-all duration-200"
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Desktop sidebar toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onMenuClick}
            className="hidden lg:flex hover:bg-white/10 transition-all duration-200"
            title={sidebarCollapsed ? "Expand sidebar (Ctrl+B)" : "Collapse sidebar (Ctrl+B)"}
          >
            {sidebarCollapsed ? <PanelLeft className="h-5 w-5" /> : <PanelLeftClose className="h-5 w-5" />}
          </Button>

          {/* Mobile title only */}
          <div className="lg:hidden">
            <h1 className="text-lg font-bold gradient-text">{title}</h1>
          </div>

          {/* Enhanced Desktop Search */}
          <div className="hidden lg:flex items-center gap-3">
            <form onSubmit={handleSearch} className="relative">
              <div className={`flex items-center transition-all duration-300 ${
                isSearchExpanded || isSearchFocused
                  ? 'w-80'
                  : 'w-64'
              }`}>
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="Search events, organizers, locations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onFocus={() => {
                      setIsSearchFocused(true)
                      setIsSearchExpanded(true)
                    }}
                    onBlur={() => setIsSearchFocused(false)}
                    className="pl-10 pr-10 bg-background/50 border-white/20 focus:border-accent/50 focus:bg-background/80 transition-all duration-300 rounded-xl"
                  />
                  {searchQuery && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={clearSearch}
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 hover:bg-muted/50"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Search suggestions/shortcuts */}
              {isSearchFocused && (
                <div className="absolute top-full left-0 right-0 mt-2 p-3 bg-card/95 backdrop-blur-sm border border-white/10 rounded-xl shadow-xl z-50">
                  <div className="text-xs text-muted-foreground mb-2">Quick search</div>
                  <div className="flex flex-wrap gap-2">
                    {["Music", "Academic", "Sports", "Food", "Arts", "Other"].map((category) => (
                      <Button
                        key={category}
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSearchQuery(category)
                          setIsSearchFocused(false)
                          // Trigger search immediately
                          router.push(`/?search=${encodeURIComponent(category)}`)
                        }}
                        className="h-7 px-3 text-xs bg-muted/50 hover:bg-accent/20"
                      >
                        {category}
                      </Button>
                    ))}
                  </div>
                  <div className="flex items-center gap-2 mt-3 pt-2 border-t border-white/10">
                    <Command className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">Press Enter to search</span>
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Notification button */}
          <Button variant="ghost" size="icon" className="hover:bg-white/10">
            <Bell className="h-5 w-5" />
          </Button>

          {/* Avatar - only shown in topbar on mobile, we'll hide on desktop since it's in sidebar */}
          <div className="relative lg:hidden">
            <Avatar className="h-10 w-10 ring-2 ring-accent/20">
              <AvatarImage src="/placeholder.svg?height=40&width=40" />
              <AvatarFallback className="bg-gradient-to-br from-primary to-accent text-white font-semibold">
                JD
              </AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-accent rounded-full border-2 border-background"></div>
          </div>
        </div>
      </div>
    </header>
  )
}
