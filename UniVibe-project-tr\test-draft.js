// Test script to create a sample draft for testing
// Run this in the browser console to create a test draft

const testDraft = {
  id: 'draft_test_123',
  title: 'Test Event from Draft',
  description: 'This is a test event created from a draft to verify the publishing functionality works correctly.',
  date: '2024-02-15',
  time: '18:00',
  location: 'University Main Hall',
  category: 'Academic',
  organizer: 'Test User',
  organizer_id: null,
  image_url: null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

// Get existing drafts or create empty object
const existingDrafts = JSON.parse(localStorage.getItem('event_drafts') || '{}');

// Add test draft
existingDrafts[testDraft.id] = testDraft;

// Save back to localStorage
localStorage.setItem('event_drafts', JSON.stringify(existingDrafts));

console.log('Test draft created:', testDraft);
console.log('All drafts:', existingDrafts);
