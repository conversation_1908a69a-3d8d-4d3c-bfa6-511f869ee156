"use client"

import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface FilterPillsProps {
  categories: string[]
  activeCategory: string
  onCategoryChange: (category: string) => void
}

export function FilterPills({ categories, activeCategory, onCategoryChange }: FilterPillsProps) {
  return (
    <div className="flex gap-3 overflow-x-auto pb-3 pt-1 scrollbar-hide">
      {categories.map((category) => (
        <Badge
          key={category}
          variant={activeCategory === category ? "default" : "outline"}
          className={cn(
            "cursor-pointer whitespace-nowrap px-6 py-3 transition-all duration-300 font-semibold text-sm border-2 select-none",
            "active:scale-95 hover:scale-105 focus:outline-none focus:ring-0 focus:ring-offset-0",
            activeCategory === category
              ? "bg-accent text-accent-foreground border-accent neon-glow scale-105 shadow-lg"
              : "border-white/20 text-muted-foreground hover:text-white hover:border-accent/50 hover:bg-accent/10",
          )}
          onClick={() => onCategoryChange(category)}
          style={{ WebkitTapHighlightColor: "transparent" }}
        >
          {category}
        </Badge>
      ))}
    </div>
  )
}
