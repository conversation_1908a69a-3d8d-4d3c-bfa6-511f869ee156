
> @supabase/auth-helpers-react@0.5.0 build /home/<USER>/work/auth-helpers/auth-helpers/packages/react
> tsup

[34mCLI[39m Building entry: src/index.tsx
[34mCLI[39m Using tsconfig: tsconfig.json
[34mCLI[39m tsup v6.7.0
[34mCLI[39m Using tsup config: /home/<USER>/work/auth-helpers/auth-helpers/packages/react/tsup.config.ts
[34mCLI[39m Target: es6
[34mCLI[39m Cleaning output folder
[34mCJS[39m Build start
[32mCJS[39m [1mdist/index.js     [22m[32m5.08 KB[39m
[32mCJS[39m [1mdist/index.js.map [22m[32m6.53 KB[39m
[32mCJS[39m ⚡️ Build success in 22ms
[34mDTS[39m Build start
[32mDTS[39m ⚡️ Build success in 2946ms
[32mDTS[39m [1mdist/index.d.ts [22m[32m1.65 KB[39m
