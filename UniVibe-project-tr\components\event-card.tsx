"use client"

import type React from "react"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Heart, MapPin, Calendar, Users, Sparkles } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { cn } from "@/lib/utils"

interface EventCardProps {
  id: string
  title: string
  date: string
  time: string
  location: string
  organizer: string
  attendees: number
  category: string
  image?: string
  isRSVPed?: boolean
}

export function EventCard({
  id,
  title,
  date,
  time,
  location,
  organizer,
  attendees,
  category,
  image,
  isRSVPed = false,
}: EventCardProps) {
  const [rsvped, setRsvped] = useState(isRSVPed)
  const [attendeeCount, setAttendeeCount] = useState(attendees)

  const handleRSVP = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setRsvped(!rsvped)
    setAttendeeCount((prev) => (rsvped ? prev - 1 : prev + 1))
  }

  return (
    <Link href={`/event/${id}`}>
      <Card className="group overflow-hidden hover:shadow-2xl hover:shadow-accent/20 transition-all duration-500 hover:scale-[1.02] bg-gradient-to-br from-card to-card/50 border border-white/10">
        <div className="relative h-48 overflow-hidden">
          <Image
            src={image || `/placeholder.svg?height=192&width=400`}
            alt={title}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-110"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />

          {/* Category Badge */}
          <Badge className="absolute top-3 left-3 bg-primary/90 text-white border-0 backdrop-blur-sm">
            <Sparkles className="w-3 h-3 mr-1" />
            {category}
          </Badge>

          {/* Gradient overlay for better text readability */}
          <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black/90 to-transparent" />
        </div>

        <CardContent className="p-5 space-y-4">
          <div>
            <h3 className="font-bold text-lg mb-2 line-clamp-2 group-hover:text-accent transition-colors duration-300">
              {title}
            </h3>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center">
                  <Calendar className="h-3 w-3 text-primary" />
                </div>
                <span className="font-medium">
                  {date} at {time}
                </span>
              </div>

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center">
                  <MapPin className="h-3 w-3 text-primary" />
                </div>
                <span className="line-clamp-1">{location}</span>
              </div>

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center">
                  <Users className="h-3 w-3 text-primary" />
                </div>
                <span className="font-medium">{attendeeCount} people going</span>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between pt-2 border-t border-white/10">
            <div>
              <p className="text-xs text-muted-foreground">Organized by</p>
              <p className="text-sm font-semibold text-accent">{organizer}</p>
            </div>

            <Button
              variant={rsvped ? "default" : "outline"}
              size="sm"
              onClick={handleRSVP}
              className={cn(
                "flex items-center gap-2 transition-all duration-300 font-semibold",
                rsvped
                  ? "bg-accent text-black hover:bg-accent/90 neon-glow"
                  : "border-accent/50 text-accent hover:bg-accent hover:text-black hover:neon-glow",
              )}
            >
              <Heart className={cn("h-4 w-4 transition-all", rsvped && "fill-current")} />
              {rsvped ? "Going!" : "RSVP"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
