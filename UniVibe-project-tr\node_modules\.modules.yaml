hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@babel/runtime@7.27.4':
    '@babel/runtime': private
  '@floating-ui/core@1.7.0':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.0':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@next/env@15.2.4':
    '@next/env': private
  '@next/swc-darwin-arm64@15.2.4':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.2.4':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.2.4':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.2.4':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.2.4':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.2.4':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.2.4':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.2.4':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/number@1.1.0':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.1':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.1(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.1(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.1(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.1(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.0(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.3(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.1(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.1(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.0(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.4(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.1(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.3(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.2(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.0.1(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.1(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.0(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.0(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.0(@types/react@19.0.0)(react@19.0.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.1.1(@types/react-dom@19.0.0)(@types/react@19.0.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.0':
    '@radix-ui/rect': private
  '@stitches/core@1.2.8':
    '@stitches/core': private
  '@supabase/auth-helpers-shared@0.7.0(@supabase/supabase-js@2.49.4)':
    '@supabase/auth-helpers-shared': private
  '@supabase/auth-js@2.69.1':
    '@supabase/auth-js': private
  '@supabase/functions-js@2.4.4':
    '@supabase/functions-js': private
  '@supabase/node-fetch@2.6.15':
    '@supabase/node-fetch': private
  '@supabase/postgrest-js@1.19.4':
    '@supabase/postgrest-js': private
  '@supabase/realtime-js@2.11.2':
    '@supabase/realtime-js': private
  '@supabase/storage-js@2.7.1':
    '@supabase/storage-js': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/phoenix@1.6.6':
    '@types/phoenix': private
  '@types/ws@8.18.1':
    '@types/ws': private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  aria-hidden@1.2.6:
    aria-hidden: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@2.0.1:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  busboy@1.6.0:
    busboy: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001720:
    caniuse-lite: private
  chokidar@3.6.0:
    chokidar: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  colorette@1.4.0:
    colorette: private
  commander@4.1.1:
    commander: private
  cookie@1.0.2:
    cookie: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  dom-helpers@5.2.1:
    dom-helpers: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.161:
    electron-to-chromium: private
  embla-carousel-reactive-utils@8.5.1(embla-carousel@8.5.1):
    embla-carousel-reactive-utils: private
  embla-carousel@8.5.1:
    embla-carousel: private
  emoji-regex@8.0.0:
    emoji-regex: private
  escalade@3.2.0:
    escalade: private
  eventemitter3@4.0.7:
    eventemitter3: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-glob@3.3.3:
    fast-glob: private
  fastq@1.19.1:
    fastq: private
  fill-range@7.1.1:
    fill-range: private
  foreground-child@3.3.1:
    foreground-child: private
  fraction.js@4.3.7:
    fraction.js: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-nonce@1.0.1:
    get-nonce: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  hasown@2.0.2:
    hasown: private
  internmap@2.0.3:
    internmap: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@2.1.0:
    isobject: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@1.21.7:
    jiti: private
  jose@4.15.9:
    jose: private
  js-tokens@4.0.0:
    js-tokens: private
  lilconfig@3.1.3:
    lilconfig: private
  line-column@1.0.2:
    line-column: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@10.4.3:
    lru-cache: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@9.0.5:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  postcss-import@15.1.0(postcss@8.5.4):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.4):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.4):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.4):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prop-types@15.8.1:
    prop-types: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-is@18.3.1:
    react-is: private
  react-remove-scroll-bar@2.3.8(@types/react@19.0.0)(react@19.0.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.0(@types/react@19.0.0)(react@19.0.0):
    react-remove-scroll: private
  react-smooth@4.0.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    react-smooth: private
  react-style-singleton@2.2.3(@types/react@19.0.0)(react@19.0.0):
    react-style-singleton: private
  react-transition-group@4.4.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    react-transition-group: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  recharts-scale@0.4.5:
    recharts-scale: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  run-parallel@1.2.0:
    run-parallel: private
  scheduler@0.25.0:
    scheduler: private
  semver@7.7.2:
    semver: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  sharp@0.33.5:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.6.1:
    source-map: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  styled-jsx@5.1.6(react@19.0.0):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@0.0.3:
    tr46: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tslib@2.8.1:
    tslib: private
  undici-types@6.11.1:
    undici-types: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  use-callback-ref@1.3.3(@types/react@19.0.0)(react@19.0.0):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@19.0.0)(react@19.0.0):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.0.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  victory-vendor@36.9.2:
    victory-vendor: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  ws@8.18.2:
    ws: private
  yaml@2.8.0:
    yaml: private
ignoredBuilds:
  - sharp
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Sun, 01 Jun 2025 08:25:41 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/runtime@1.4.3'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@next/swc-darwin-arm64@15.2.4'
  - '@next/swc-darwin-x64@15.2.4'
  - '@next/swc-linux-arm64-gnu@15.2.4'
  - '@next/swc-linux-arm64-musl@15.2.4'
  - '@next/swc-linux-x64-gnu@15.2.4'
  - '@next/swc-linux-x64-musl@15.2.4'
  - '@next/swc-win32-arm64-msvc@15.2.4'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\OneDrive\Desktop\UniVibe\UniVibe-project-tr\node_modules\.pnpm
virtualStoreDirMaxLength: 60
