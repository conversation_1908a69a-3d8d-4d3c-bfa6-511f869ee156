"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-separator@1_8abea9ee688ef906aae3ef98df174554";
exports.ids = ["vendor-chunks/@radix-ui+react-separator@1_8abea9ee688ef906aae3ef98df174554"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-separator@1_8abea9ee688ef906aae3ef98df174554/node_modules/@radix-ui/react-separator/dist/index.mjs":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-separator@1_8abea9ee688ef906aae3ef98df174554/node_modules/@radix-ui/react-separator/dist/index.mjs ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_089d11c4c22a707804923fd2d02ee237/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/separator/src/Separator.tsx\n\n\n\nvar NAME = \"Separator\";\nvar DEFAULT_ORIENTATION = \"horizontal\";\nvar ORIENTATIONS = [\"horizontal\", \"vertical\"];\nvar Separator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  const ariaOrientation = orientation === \"vertical\" ? orientation : void 0;\n  const semanticProps = decorative ? { role: \"none\" } : { \"aria-orientation\": ariaOrientation, role: \"separator\" };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.div,\n    {\n      \"data-orientation\": orientation,\n      ...semanticProps,\n      ...domProps,\n      ref: forwardedRef\n    }\n  );\n});\nSeparator.displayName = NAME;\nfunction isValidOrientation(orientation) {\n  return ORIENTATIONS.includes(orientation);\n}\nvar Root = Separator;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-separator@1_8abea9ee688ef906aae3ef98df174554/node_modules/@radix-ui/react-separator/dist/index.mjs\n");

/***/ })

};
;