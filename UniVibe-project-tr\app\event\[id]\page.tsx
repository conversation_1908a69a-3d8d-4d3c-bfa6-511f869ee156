"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Heart, Share2, MapPin, Calendar, Users, User, FileText, Sparkles, Clock } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState, useEffect, use } from "react"
import { cn } from "@/lib/utils"
import { getEventById } from "@/lib/events"

// Mock event data
const mockEvent = {
  id: "1",
  title: "Spring Music Festival 2024",
  date: "March 15, 2024",
  time: "7:00 PM - 11:00 PM",
  location: "University Quad, Main Campus",
  organizer: "Music Society",
  organizerAvatar: "/placeholder.svg?height=40&width=40",
  attendees: 234,
  category: "Music",
  image: "/placeholder.svg?height=300&width=600",
  description: `Join us for the biggest music event of the spring semester! The Spring Music Festival features performances from local bands, student artists, and special guest performers.

🎵 Featured Acts:
- The Campus Collective
- Midnight Echoes  
- DJ Sarah K
- Open Mic Session

🍕 Food trucks will be available
🎟️ Free admission for all students
🎁 Prizes and giveaways throughout the night

Bring your friends and enjoy an unforgettable evening of music, food, and community. Don't forget to bring your student ID for entry!`,
  isRSVPed: false,
}

export default function EventDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const [event, setEvent] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [rsvped, setRsvped] = useState(false)
  const [attendeeCount, setAttendeeCount] = useState(0)

  // Fetch event data
  useEffect(() => {
    const fetchEvent = async () => {
      try {
        const { data, error } = await getEventById(resolvedParams.id)
        if (error) {
          console.error('Error fetching event:', error)
          // Fallback to mock data
          setEvent(mockEvent)
          setRsvped(mockEvent.isRSVPed)
          setAttendeeCount(mockEvent.attendees)
        } else if (data) {
          setEvent(data)
          setRsvped(false) // TODO: Check if user has RSVPed
          setAttendeeCount(data.attendees_count || 0)
        } else {
          // Event not found, use mock data
          setEvent(mockEvent)
          setRsvped(mockEvent.isRSVPed)
          setAttendeeCount(mockEvent.attendees)
        }
      } catch (error) {
        console.error('Unexpected error fetching event:', error)
        setEvent(mockEvent)
        setRsvped(mockEvent.isRSVPed)
        setAttendeeCount(mockEvent.attendees)
      } finally {
        setLoading(false)
      }
    }

    fetchEvent()
  }, [resolvedParams.id])

  const handleRSVP = () => {
    setRsvped(!rsvped)
    setAttendeeCount((prev) => (rsvped ? prev - 1 : prev + 1))
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: event?.title || 'Event',
          text: `Check out this event: ${event?.title || 'Event'}`,
          url: window.location.href,
        })
      } catch (error) {
        console.log("Error sharing:", error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert("Event link copied to clipboard!")
    }
  }

  // Helper function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "TBD"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Helper function to format time
  const formatTime = (timeString: string) => {
    if (!timeString) return "TBD"
    const [hours, minutes] = timeString.split(':')
    const date = new Date()
    date.setHours(parseInt(hours), parseInt(minutes))
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          <div className="h-64 bg-muted rounded-lg"></div>
          <div className="h-8 bg-muted rounded w-3/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="p-4 text-center">
        <h1 className="text-2xl font-bold mb-2">Event Not Found</h1>
        <p className="text-muted-foreground mb-4">The event you're looking for doesn't exist.</p>
        <Link href="/">
          <Button>Back to Events</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      {/* Back button */}
      <Link href="/">
        <Button variant="ghost" size="icon" className="absolute top-4 left-4 z-10 bg-background/80 backdrop-blur-sm hover:bg-background/90 transition-all">
          <ArrowLeft className="h-5 w-5" />
        </Button>
      </Link>

      {/* Desktop Layout */}
      <div className="hidden lg:block">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Left Column - Event Poster */}
            <div className="space-y-6">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl border border-white/10">
                <div className="aspect-[4/5] relative">
                  <Image
                    src={event.image_url || "/placeholder.svg"}
                    alt={event.title}
                    fill
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />
                  <Badge className="absolute top-4 left-4 bg-primary/90 text-white border-0 backdrop-blur-sm">
                    <Sparkles className="w-3 h-3 mr-1" />
                    {event.category}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Right Column - Event Details */}
            <div className="space-y-8">
              {/* Title and Actions */}
              <div className="space-y-6">
                <div>
                  <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                    {event.title}
                  </h1>
                  <div className="flex items-center gap-3 text-muted-foreground">
                    <User className="h-4 w-4" />
                    <span>Organized by {event.organizer}</span>
                  </div>
                </div>

                <div className="flex gap-3">
                  <Button
                    onClick={handleRSVP}
                    size="lg"
                    className={cn(
                      "flex-1 flex items-center gap-2 h-12",
                      rsvped ? "bg-accent hover:bg-accent/90 text-accent-foreground" : "bg-primary hover:bg-primary/90",
                    )}
                  >
                    <Heart className={cn("h-5 w-5", rsvped && "fill-current")} />
                    {rsvped ? "Going!" : "I'm Interested"}
                  </Button>

                  <Button variant="outline" size="lg" onClick={handleShare} className="border-white/20 hover:bg-accent/10">
                    <Share2 className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Event Details Card */}
              <Card className="shadow-xl border-white/10 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm">
                <CardContent className="p-6 space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20 border border-primary/30">
                      <Calendar className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <p className="font-semibold text-lg">{formatDate(event.date)}</p>
                      <p className="text-muted-foreground">{formatTime(event.time)}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30">
                      <MapPin className="h-6 w-6 text-accent" />
                    </div>
                    <div>
                      <p className="font-semibold">{event.location}</p>
                      <p className="text-sm text-muted-foreground">Event Location</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20 border border-primary/30">
                      <Users className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <p className="font-semibold">{attendeeCount} people going</p>
                      <p className="text-sm text-muted-foreground">Join the community</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Description */}
              <Card className="shadow-xl border-white/10 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-xl mb-4 flex items-center gap-2">
                    <FileText className="h-5 w-5 text-accent" />
                    About this event
                  </h3>
                  <div className="prose prose-sm max-w-none text-muted-foreground">
                    {(event.description || 'No description available.').split("\n").map((paragraph, index) => (
                      <p key={index} className="mb-3 last:mb-0 leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Enhanced Mobile Header with Larger Poster */}
        <div className="relative">
          {/* Large Poster Image */}
          <div className="relative h-[70vh] min-h-[500px] bg-muted">
            <Image
              src={event.image_url || "/placeholder.svg"}
              alt={event.title}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />

            {/* Category Badge */}
            <Badge className="absolute top-4 left-4 bg-primary/90 text-white border-0 backdrop-blur-sm">
              <Sparkles className="w-3 h-3 mr-1" />
              {event.category}
            </Badge>

            {/* Floating Content Overlay */}
            <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
              <div className="space-y-4">
                <div>
                  <h1 className="text-3xl font-bold mb-2 leading-tight">
                    {event.title}
                  </h1>
                  <div className="flex items-center gap-2 text-white/80">
                    <User className="h-4 w-4" />
                    <span>Organized by {event.organizer}</span>
                  </div>
                </div>

                {/* Quick Info Cards */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-black/40 backdrop-blur-sm rounded-lg p-3 border border-white/20">
                    <div className="flex items-center gap-2 mb-1">
                      <Calendar className="h-4 w-4 text-accent" />
                      <span className="text-xs text-white/70 uppercase tracking-wide">Date</span>
                    </div>
                    <p className="font-semibold text-sm">{formatDate(event.date)}</p>
                  </div>
                  <div className="bg-black/40 backdrop-blur-sm rounded-lg p-3 border border-white/20">
                    <div className="flex items-center gap-2 mb-1">
                      <Users className="h-4 w-4 text-primary" />
                      <span className="text-xs text-white/70 uppercase tracking-wide">Going</span>
                    </div>
                    <p className="font-semibold text-sm">{attendeeCount} people</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-2">
                  <Button
                    onClick={handleRSVP}
                    size="lg"
                    className={cn(
                      "flex-1 flex items-center gap-2 h-12 font-semibold",
                      rsvped ? "bg-accent hover:bg-accent/90 text-accent-foreground" : "bg-primary hover:bg-primary/90",
                    )}
                  >
                    <Heart className={cn("h-5 w-5", rsvped && "fill-current")} />
                    {rsvped ? "Going!" : "I'm Interested"}
                  </Button>

                  <Button
                    variant="outline"
                    size="lg"
                    onClick={handleShare}
                    className="border-white/30 bg-black/20 backdrop-blur-sm hover:bg-white/10 text-white hover:text-white"
                  >
                    <Share2 className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="p-6 space-y-6 bg-gradient-to-br from-background via-background to-primary/5">
          {/* Enhanced Event Details */}
          <Card className="shadow-xl border-white/10 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm">
            <CardContent className="p-6 space-y-6">
              <h3 className="font-semibold text-xl mb-4 flex items-center gap-2">
                <Calendar className="h-5 w-5 text-accent" />
                Event Details
              </h3>

              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 rounded-lg bg-background/50 border border-white/10">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20 border border-primary/30">
                    <Clock className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-semibold">{formatTime(event.time)}</p>
                    <p className="text-sm text-muted-foreground">Event Time</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 rounded-lg bg-background/50 border border-white/10">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30">
                    <MapPin className="h-5 w-5 text-accent" />
                  </div>
                  <div>
                    <p className="font-semibold">{event.location}</p>
                    <p className="text-sm text-muted-foreground">Event Location</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Description */}
          <Card className="shadow-xl border-white/10 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm">
            <CardContent className="p-6">
              <h3 className="font-semibold text-xl mb-4 flex items-center gap-2">
                <FileText className="h-5 w-5 text-accent" />
                About this event
              </h3>
              <div className="prose prose-sm max-w-none text-muted-foreground">
                {(event.description || 'No description available.').split("\n").map((paragraph, index) => (
                  <p key={index} className="mb-3 last:mb-0 leading-relaxed">
                    {paragraph}
                  </p>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
