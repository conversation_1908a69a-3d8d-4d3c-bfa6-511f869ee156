"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-focus-guard_543da02c6e51106b6041de5427815025";
exports.ids = ["vendor-chunks/@radix-ui+react-focus-guard_543da02c6e51106b6041de5427815025"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_543da02c6e51106b6041de5427815025/node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-focus-guard_543da02c6e51106b6041de5427815025/node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/FocusGuards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusGuards.useEffect\": ()=>{\n            const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n            document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n            document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n            count++;\n            return ({\n                \"useFocusGuards.useEffect\": ()=>{\n                    if (count === 1) {\n                        document.querySelectorAll(\"[data-radix-focus-guard]\").forEach({\n                            \"useFocusGuards.useEffect\": (node)=>node.remove()\n                        }[\"useFocusGuards.useEffect\"]);\n                    }\n                    count--;\n                }\n            })[\"useFocusGuards.useEffect\"];\n        }\n    }[\"useFocusGuards.useEffect\"], []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_543da02c6e51106b6041de5427815025/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ })

};
;