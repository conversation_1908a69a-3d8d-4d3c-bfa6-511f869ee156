# UniVibe - University Event Hub

A modern, responsive university event management platform built with Next.js 15, TypeScript, and Supabase.

## ✨ Features

- **🎯 Event Discovery**: Browse and filter events by category
- **📱 Responsive Design**: Works seamlessly on all devices
- **🎨 Modern UI**: Dark theme with gradient accents and smooth animations
- **🔐 Authentication**: Secure user authentication with Supabase
- **📅 Event Management**: Create, edit, and manage events
- **💫 RSVP System**: Easy event registration and attendance tracking
- **🔔 Notifications**: Real-time notifications for event updates
- **👤 User Profiles**: Personalized user profiles and preferences
- **⌨️ Keyboard Shortcuts**: Efficient navigation with keyboard shortcuts
- **🎛️ Collapsible Sidebar**: Toggle sidebar for better screen space utilization

## 🚀 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Icons**: Lucide React
- **Package Manager**: pnpm

## 🛠️ Setup Instructions

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm
- Supabase account

### 1. Clone and Install

```bash
git clone <repository-url>
cd UniVibe-project-tr
pnpm install
```

### 2. Supabase Setup

1. Create a new project at [supabase.com](https://supabase.com)
2. Copy your project URL and anon key
3. Update `.env.local` with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

### 3. Database Setup

Run the migration to create tables:

```sql
-- Copy and paste the contents of supabase/migrations/001_initial_schema.sql
-- into your Supabase SQL editor and run it
```

Optionally, add sample data:

```sql
-- Copy and paste the contents of supabase/seed.sql
-- into your Supabase SQL editor and run it
```

### 4. Run Development Server

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## ⌨️ Keyboard Shortcuts

- **Ctrl/Cmd + B**: Toggle sidebar collapse
- **Escape**: Close mobile sidebar

## 📁 Project Structure

```
UniVibe-project-tr/
├── app/                    # Next.js App Router pages
├── components/             # React components
│   ├── ui/                # Reusable UI components
│   ├── sidebar.tsx        # Enhanced sidebar with collapse
│   ├── top-bar.tsx        # Navigation header
│   └── app-layout.tsx     # Main layout wrapper
├── lib/                   # Utility functions
│   ├── supabase.ts        # Supabase client configuration
│   └── utils.ts           # General utilities
├── supabase/              # Database files
│   ├── migrations/        # SQL migration files
│   └── seed.sql          # Sample data
├── public/                # Static assets
└── styles/                # Global styles
```

## 🚀 Deployment

The project can be deployed on Vercel, Netlify, or any platform that supports Next.js.

## 📝 License

This project is open source and available under the [MIT License](LICENSE).
