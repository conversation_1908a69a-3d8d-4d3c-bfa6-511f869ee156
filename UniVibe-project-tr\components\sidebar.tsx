"use client"

import { Home, Plus, Bell, User, Settings, LogOut, X, Sparkles, ChevronRight, FileText } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useAuth } from "@/lib/auth-context"
import { useEffect } from "react"

interface SidebarProps {
  isOpen: boolean
  isCollapsed: boolean
  onClose: () => void
  onToggleCollapse: () => void
}

export function Sidebar({ isOpen, isCollapsed, onClose, onToggleCollapse }: SidebarProps) {
  const pathname = usePathname()
  const { user, profile, signOut } = useAuth()

  // Prevent body scroll when sidebar is open on mobile
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "unset"
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "unset"
    }
  }, [isOpen])

  const navItems = [
    { href: "/", icon: Home, label: "Home", description: "Discover events" },
    { href: "/post", icon: Plus, label: "Post Event", description: "Create new event" },
    { href: "/drafts", icon: FileText, label: "Drafts", description: "Manage saved drafts" },
    { href: "/notifications", icon: Bell, label: "Notifications", description: "Stay updated" },
    { href: "/profile", icon: User, label: "Profile", description: "Your account" },
  ]

  const secondaryItems = [{ href: "/settings", icon: Settings, label: "Settings", description: "App preferences" }]

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 sidebar-overlay lg:hidden"
          onClick={onClose}
          style={{ touchAction: "none" }} // Prevent scroll on overlay
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed top-0 left-0 z-50 h-full bg-card border-r border-white/10 transform transition-all duration-300 ease-in-out lg:translate-x-0 flex flex-col",
          isCollapsed ? "w-20" : "w-80",
          isOpen ? "translate-x-0" : "-translate-x-full",
        )}
        style={{ touchAction: "pan-y" }} // Allow vertical scrolling only
      >
        {/* Header - Fixed */}
        <div className={cn("flex-shrink-0 border-b border-white/10", isCollapsed ? "p-3" : "p-6")}>
          <div className={cn("flex items-center mb-6", isCollapsed ? "justify-center" : "justify-between")}>
            {!isCollapsed && (
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-br from-primary via-primary to-accent rounded-xl flex items-center justify-center shadow-lg">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <div className="absolute -inset-1 bg-gradient-to-br from-primary to-accent rounded-xl blur opacity-30 animate-pulse"></div>
                </div>
                <div>
                  <h1 className="text-lg font-bold gradient-text">UniVibe</h1>
                  <p className="text-xs text-muted-foreground">University Hub</p>
                </div>
              </div>
            )}

            {isCollapsed && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="relative">
                      <div className="w-10 h-10 bg-gradient-to-br from-primary via-primary to-accent rounded-xl flex items-center justify-center shadow-lg">
                        <Sparkles className="w-5 h-5 text-white" />
                      </div>
                      <div className="absolute -inset-1 bg-gradient-to-br from-primary to-accent rounded-xl blur opacity-30 animate-pulse"></div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>UniVibe - University Hub</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            <Button variant="ghost" size="icon" onClick={onClose} className="lg:hidden hover:bg-white/10">
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* User Profile */}
          {user && !isCollapsed && (
            <div className="flex items-center gap-3 p-3 rounded-xl bg-white/5 border border-white/10">
              <Avatar className="h-12 w-12 ring-2 ring-accent/20">
                <AvatarImage src={profile?.avatar_url || "/placeholder.svg?height=48&width=48"} />
                <AvatarFallback className="bg-gradient-to-br from-primary to-accent text-white font-semibold">
                  {profile?.full_name?.split(' ').map(n => n[0]).join('') || user.email?.[0]?.toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="font-semibold text-sm">{profile?.full_name || 'User'}</p>
                <p className="text-xs text-muted-foreground truncate">{user.email}</p>
                {profile?.role && (
                  <p className="text-xs text-accent capitalize">{profile.role}</p>
                )}
              </div>
            </div>
          )}

          {user && isCollapsed && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex justify-center">
                    <Avatar className="h-10 w-10 ring-2 ring-accent/20">
                      <AvatarImage src={profile?.avatar_url || "/placeholder.svg?height=40&width=40"} />
                      <AvatarFallback className="bg-gradient-to-br from-primary to-accent text-white font-semibold text-sm">
                        {profile?.full_name?.split(' ').map(n => n[0]).join('') || user.email?.[0]?.toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <div>
                    <p className="font-semibold">{profile?.full_name || 'User'}</p>
                    <p className="text-xs text-muted-foreground">{user.email}</p>
                    {profile?.role && (
                      <p className="text-xs text-accent capitalize">{profile.role}</p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>

        {/* Navigation - Scrollable */}
        <nav className={cn("flex-1 overflow-y-auto space-y-2", isCollapsed ? "p-3" : "p-6")}>
          <div className="space-y-1">
            {navItems.map((item) => {
              const isActive = pathname === item.href

              if (isCollapsed) {
                return (
                  <TooltipProvider key={item.href}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link
                          href={item.href}
                          onClick={onClose}
                          className={cn(
                            "flex items-center justify-center p-3 rounded-xl transition-all duration-300 group relative",
                            isActive
                              ? "bg-accent text-accent-foreground shadow-lg neon-glow"
                              : "text-muted-foreground hover:text-white hover:bg-white/10",
                          )}
                        >
                          {isActive && (
                            <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-accent rounded-r-full" />
                          )}
                          <item.icon className="h-5 w-5" />
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent side="right">
                        <div>
                          <p className="font-semibold">{item.label}</p>
                          <p className="text-xs text-muted-foreground">{item.description}</p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )
              }

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={onClose}
                  className={cn(
                    "flex items-center gap-4 p-4 rounded-xl transition-all duration-300 group relative",
                    isActive
                      ? "bg-accent text-accent-foreground shadow-lg neon-glow"
                      : "text-muted-foreground hover:text-white hover:bg-white/10",
                  )}
                >
                  {isActive && (
                    <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-accent rounded-r-full" />
                  )}
                  <div
                    className={cn(
                      "p-2 rounded-lg transition-colors",
                      isActive ? "bg-white/20" : "bg-white/5 group-hover:bg-white/10",
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <p className={cn("font-semibold text-sm", isActive && "text-accent-foreground")}>{item.label}</p>
                    <p className={cn("text-xs", isActive ? "text-accent-foreground/80" : "text-muted-foreground")}>
                      {item.description}
                    </p>
                  </div>
                </Link>
              )
            })}
          </div>

          <div className="pt-6 border-t border-white/10 space-y-1">
            {secondaryItems.map((item) => {
              const isActive = pathname === item.href

              if (isCollapsed) {
                return (
                  <TooltipProvider key={item.href}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link
                          href={item.href}
                          onClick={onClose}
                          className={cn(
                            "flex items-center justify-center p-3 rounded-xl transition-all duration-300 group",
                            isActive
                              ? "bg-accent text-accent-foreground shadow-lg neon-glow"
                              : "text-muted-foreground hover:text-white hover:bg-white/10",
                          )}
                        >
                          <item.icon className="h-5 w-5" />
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent side="right">
                        <div>
                          <p className="font-semibold">{item.label}</p>
                          <p className="text-xs text-muted-foreground">{item.description}</p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )
              }

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={onClose}
                  className={cn(
                    "flex items-center gap-4 p-4 rounded-xl transition-all duration-300 group",
                    isActive
                      ? "bg-accent text-accent-foreground shadow-lg neon-glow"
                      : "text-muted-foreground hover:text-white hover:bg-white/10",
                  )}
                >
                  <div
                    className={cn(
                      "p-2 rounded-lg transition-colors",
                      isActive ? "bg-white/20" : "bg-white/5 group-hover:bg-white/10",
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <p className={cn("font-semibold text-sm", isActive && "text-accent-foreground")}>{item.label}</p>
                    <p className={cn("text-xs", isActive ? "text-accent-foreground/80" : "text-muted-foreground")}>
                      {item.description}
                    </p>
                  </div>
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Footer - Fixed */}
        {user && (
          <div className={cn("flex-shrink-0 border-t border-white/10", isCollapsed ? "p-3" : "p-6")}>
            {isCollapsed ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => signOut()}
                      className="w-full p-3 text-muted-foreground hover:text-white hover:bg-white/10 transition-all duration-300"
                    >
                      <LogOut className="h-5 w-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>Sign Out</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <Button
                variant="ghost"
                onClick={() => signOut()}
                className="w-full justify-start gap-4 p-4 text-muted-foreground hover:text-white hover:bg-white/10 transition-all duration-300"
              >
                <div className="p-2 rounded-lg bg-white/5">
                  <LogOut className="h-5 w-5" />
                </div>
                <span className="font-semibold text-sm">Sign Out</span>
              </Button>
            )}
          </div>
        )}
      </aside>
    </>
  )
}
