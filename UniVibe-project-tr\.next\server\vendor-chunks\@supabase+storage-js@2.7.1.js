"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+storage-js@2.7.1";
exports.ids = ["vendor-chunks/@supabase+storage-js@2.7.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StorageClient: () => (/* binding */ StorageClient)\n/* harmony export */ });\n/* harmony import */ var _packages_StorageFileApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./packages/StorageFileApi */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js\");\n/* harmony import */ var _packages_StorageBucketApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./packages/StorageBucketApi */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js\");\n\n\nclass StorageClient extends _packages_StorageBucketApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n    constructor(url, headers = {}, fetch) {\n        super(url, headers, fetch);\n    }\n    /**\n     * Perform file operation in a bucket.\n     *\n     * @param id The bucket id to operate on.\n     */\n    from(id) {\n        return new _packages_StorageFileApi__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this.url, this.headers, id, this.fetch);\n    }\n}\n//# sourceMappingURL=StorageClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N0b3JhZ2UtanNAMi43LjEvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zdG9yYWdlLWpzL2Rpc3QvbW9kdWxlL1N0b3JhZ2VDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVEO0FBQ0k7QUFDcEQsNEJBQTRCLGtFQUFnQjtBQUNuRCxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixnRUFBYztBQUNqQztBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdG9yYWdlLWpzQDIuNy4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3RvcmFnZS1qc1xcZGlzdFxcbW9kdWxlXFxTdG9yYWdlQ2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTdG9yYWdlRmlsZUFwaSBmcm9tICcuL3BhY2thZ2VzL1N0b3JhZ2VGaWxlQXBpJztcbmltcG9ydCBTdG9yYWdlQnVja2V0QXBpIGZyb20gJy4vcGFja2FnZXMvU3RvcmFnZUJ1Y2tldEFwaSc7XG5leHBvcnQgY2xhc3MgU3RvcmFnZUNsaWVudCBleHRlbmRzIFN0b3JhZ2VCdWNrZXRBcGkge1xuICAgIGNvbnN0cnVjdG9yKHVybCwgaGVhZGVycyA9IHt9LCBmZXRjaCkge1xuICAgICAgICBzdXBlcih1cmwsIGhlYWRlcnMsIGZldGNoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUGVyZm9ybSBmaWxlIG9wZXJhdGlvbiBpbiBhIGJ1Y2tldC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBpZCBUaGUgYnVja2V0IGlkIHRvIG9wZXJhdGUgb24uXG4gICAgICovXG4gICAgZnJvbShpZCkge1xuICAgICAgICByZXR1cm4gbmV3IFN0b3JhZ2VGaWxlQXBpKHRoaXMudXJsLCB0aGlzLmhlYWRlcnMsIGlkLCB0aGlzLmZldGNoKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1TdG9yYWdlQ2xpZW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/constants.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/constants.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/version.js\");\n\nconst DEFAULT_HEADERS = { 'X-Client-Info': `storage-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N0b3JhZ2UtanNAMi43LjEvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zdG9yYWdlLWpzL2Rpc3QvbW9kdWxlL2xpYi9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFDN0IsMEJBQTBCLCtCQUErQiw2Q0FBTyxDQUFDO0FBQ3hFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHphY2hhXFxPbmVEcml2ZVxcRGVza3RvcFxcVW5pVmliZVxcVW5pVmliZS1wcm9qZWN0LXRyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2Urc3RvcmFnZS1qc0AyLjcuMVxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN0b3JhZ2UtanNcXGRpc3RcXG1vZHVsZVxcbGliXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdmVyc2lvbiB9IGZyb20gJy4vdmVyc2lvbic7XG5leHBvcnQgY29uc3QgREVGQVVMVF9IRUFERVJTID0geyAnWC1DbGllbnQtSW5mbyc6IGBzdG9yYWdlLWpzLyR7dmVyc2lvbn1gIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StorageApiError: () => (/* binding */ StorageApiError),\n/* harmony export */   StorageError: () => (/* binding */ StorageError),\n/* harmony export */   StorageUnknownError: () => (/* binding */ StorageUnknownError),\n/* harmony export */   isStorageError: () => (/* binding */ isStorageError)\n/* harmony export */ });\nclass StorageError extends Error {\n    constructor(message) {\n        super(message);\n        this.__isStorageError = true;\n        this.name = 'StorageError';\n    }\n}\nfunction isStorageError(error) {\n    return typeof error === 'object' && error !== null && '__isStorageError' in error;\n}\nclass StorageApiError extends StorageError {\n    constructor(message, status) {\n        super(message);\n        this.name = 'StorageApiError';\n        this.status = status;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n        };\n    }\n}\nclass StorageUnknownError extends StorageError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'StorageUnknownError';\n        this.originalError = originalError;\n    }\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N0b3JhZ2UtanNAMi43LjEvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zdG9yYWdlLWpzL2Rpc3QvbW9kdWxlL2xpYi9lcnJvcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdG9yYWdlLWpzQDIuNy4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3RvcmFnZS1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXGVycm9ycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgU3RvcmFnZUVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMuX19pc1N0b3JhZ2VFcnJvciA9IHRydWU7XG4gICAgICAgIHRoaXMubmFtZSA9ICdTdG9yYWdlRXJyb3InO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBpc1N0b3JhZ2VFcnJvcihlcnJvcikge1xuICAgIHJldHVybiB0eXBlb2YgZXJyb3IgPT09ICdvYmplY3QnICYmIGVycm9yICE9PSBudWxsICYmICdfX2lzU3RvcmFnZUVycm9yJyBpbiBlcnJvcjtcbn1cbmV4cG9ydCBjbGFzcyBTdG9yYWdlQXBpRXJyb3IgZXh0ZW5kcyBTdG9yYWdlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIHN0YXR1cykge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgdGhpcy5uYW1lID0gJ1N0b3JhZ2VBcGlFcnJvcic7XG4gICAgICAgIHRoaXMuc3RhdHVzID0gc3RhdHVzO1xuICAgIH1cbiAgICB0b0pTT04oKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXG4gICAgICAgICAgICBtZXNzYWdlOiB0aGlzLm1lc3NhZ2UsXG4gICAgICAgICAgICBzdGF0dXM6IHRoaXMuc3RhdHVzLFxuICAgICAgICB9O1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBTdG9yYWdlVW5rbm93bkVycm9yIGV4dGVuZHMgU3RvcmFnZUVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihtZXNzYWdlLCBvcmlnaW5hbEVycm9yKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLm5hbWUgPSAnU3RvcmFnZVVua25vd25FcnJvcic7XG4gICAgICAgIHRoaXMub3JpZ2luYWxFcnJvciA9IG9yaWdpbmFsRXJyb3I7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXJyb3JzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   head: () => (/* binding */ head),\n/* harmony export */   post: () => (/* binding */ post),\n/* harmony export */   put: () => (/* binding */ put),\n/* harmony export */   remove: () => (/* binding */ remove)\n/* harmony export */ });\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst handleError = (error, reject, options) => __awaiter(void 0, void 0, void 0, function* () {\n    const Res = yield (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.resolveResponse)();\n    if (error instanceof Res && !(options === null || options === void 0 ? void 0 : options.noResolveJson)) {\n        error\n            .json()\n            .then((err) => {\n            reject(new _errors__WEBPACK_IMPORTED_MODULE_1__.StorageApiError(_getErrorMessage(err), error.status || 500));\n        })\n            .catch((err) => {\n            reject(new _errors__WEBPACK_IMPORTED_MODULE_1__.StorageUnknownError(_getErrorMessage(err), err));\n        });\n    }\n    else {\n        reject(new _errors__WEBPACK_IMPORTED_MODULE_1__.StorageUnknownError(_getErrorMessage(error), error));\n    }\n});\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET') {\n        return params;\n    }\n    params.headers = Object.assign({ 'Content-Type': 'application/json' }, options === null || options === void 0 ? void 0 : options.headers);\n    if (body) {\n        params.body = JSON.stringify(body);\n    }\n    return Object.assign(Object.assign({}, params), parameters);\n};\nfunction _handleRequest(fetcher, method, url, options, parameters, body) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            fetcher(url, _getRequestParams(method, options, parameters, body))\n                .then((result) => {\n                if (!result.ok)\n                    throw result;\n                if (options === null || options === void 0 ? void 0 : options.noResolveJson)\n                    return result;\n                return result.json();\n            })\n                .then((data) => resolve(data))\n                .catch((error) => handleError(error, reject, options));\n        });\n    });\n}\nfunction get(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'GET', url, options, parameters);\n    });\n}\nfunction post(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'POST', url, options, parameters, body);\n    });\n}\nfunction put(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'PUT', url, options, parameters, body);\n    });\n}\nfunction head(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'HEAD', url, Object.assign(Object.assign({}, options), { noResolveJson: true }), parameters);\n    });\n}\nfunction remove(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'DELETE', url, options, parameters, body);\n    });\n}\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   recursiveToCamel: () => (/* binding */ recursiveToCamel),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveResponse: () => (/* binding */ resolveResponse)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst resolveResponse = () => __awaiter(void 0, void 0, void 0, function* () {\n    if (typeof Response === 'undefined') {\n        // @ts-ignore\n        return (yield Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23))).Response;\n    }\n    return Response;\n});\nconst recursiveToCamel = (item) => {\n    if (Array.isArray(item)) {\n        return item.map((el) => recursiveToCamel(el));\n    }\n    else if (typeof item === 'function' || item !== Object(item)) {\n        return item;\n    }\n    const result = {};\n    Object.entries(item).forEach(([key, value]) => {\n        const newKey = key.replace(/([-_][a-z])/gi, (c) => c.toUpperCase().replace(/[-_]/g, ''));\n        result[newKey] = recursiveToCamel(value);\n    });\n    return result;\n};\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/version.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/version.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n// generated by genversion\nconst version = '2.7.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N0b3JhZ2UtanNAMi43LjEvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zdG9yYWdlLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdG9yYWdlLWpzQDIuNy4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3RvcmFnZS1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZ2VuZXJhdGVkIGJ5IGdlbnZlcnNpb25cbmV4cG9ydCBjb25zdCB2ZXJzaW9uID0gJzIuNy4xJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StorageBucketApi)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/errors */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\nclass StorageBucketApi {\n    constructor(url, headers = {}, fetch) {\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_HEADERS), headers);\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(fetch);\n    }\n    /**\n     * Retrieves the details of all Storage buckets within an existing project.\n     */\n    listBuckets() {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/bucket`, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing Storage bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to retrieve.\n     */\n    getBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/bucket/${id}`, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a new Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are creating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     * @returns newly created bucket id\n     */\n    createBucket(id, options = {\n        public: false,\n    }) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/bucket`, {\n                    id,\n                    name: id,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Updates a Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are updating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     */\n    updateBucket(id, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.put)(this.fetch, `${this.url}/bucket/${id}`, {\n                    id,\n                    name: id,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Removes all objects inside a single bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to empty.\n     */\n    emptyBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/bucket/${id}/empty`, {}, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n     * You must first `empty()` the bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to delete.\n     */\n    deleteBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.remove)(this.fetch, `${this.url}/bucket/${id}`, {}, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n}\n//# sourceMappingURL=StorageBucketApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StorageFileApi)\n/* harmony export */ });\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/errors */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\nconst DEFAULT_SEARCH_OPTIONS = {\n    limit: 100,\n    offset: 0,\n    sortBy: {\n        column: 'name',\n        order: 'asc',\n    },\n};\nconst DEFAULT_FILE_OPTIONS = {\n    cacheControl: '3600',\n    contentType: 'text/plain;charset=UTF-8',\n    upsert: false,\n};\nclass StorageFileApi {\n    constructor(url, headers = {}, bucketId, fetch) {\n        this.url = url;\n        this.headers = headers;\n        this.bucketId = bucketId;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_0__.resolveFetch)(fetch);\n    }\n    /**\n     * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n     *\n     * @param method HTTP method.\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    uploadOrUpdate(method, path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let body;\n                const options = Object.assign(Object.assign({}, DEFAULT_FILE_OPTIONS), fileOptions);\n                let headers = Object.assign(Object.assign({}, this.headers), (method === 'POST' && { 'x-upsert': String(options.upsert) }));\n                const metadata = options.metadata;\n                if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append('cacheControl', options.cacheControl);\n                    if (metadata) {\n                        body.append('metadata', this.encodeMetadata(metadata));\n                    }\n                    body.append('', fileBody);\n                }\n                else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append('cacheControl', options.cacheControl);\n                    if (metadata) {\n                        body.append('metadata', this.encodeMetadata(metadata));\n                    }\n                }\n                else {\n                    body = fileBody;\n                    headers['cache-control'] = `max-age=${options.cacheControl}`;\n                    headers['content-type'] = options.contentType;\n                    if (metadata) {\n                        headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata));\n                    }\n                }\n                if (fileOptions === null || fileOptions === void 0 ? void 0 : fileOptions.headers) {\n                    headers = Object.assign(Object.assign({}, headers), fileOptions.headers);\n                }\n                const cleanPath = this._removeEmptyFolders(path);\n                const _path = this._getFinalPath(cleanPath);\n                const res = yield this.fetch(`${this.url}/object/${_path}`, Object.assign({ method, body: body, headers }, ((options === null || options === void 0 ? void 0 : options.duplex) ? { duplex: options.duplex } : {})));\n                const data = yield res.json();\n                if (res.ok) {\n                    return {\n                        data: { path: cleanPath, id: data.Id, fullPath: data.Key },\n                        error: null,\n                    };\n                }\n                else {\n                    const error = data;\n                    return { data: null, error };\n                }\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Uploads a file to an existing bucket.\n     *\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    upload(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.uploadOrUpdate('POST', path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Upload a file with a token generated from `createSignedUploadUrl`.\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param token The token generated from `createSignedUploadUrl`\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    uploadToSignedUrl(path, token, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const cleanPath = this._removeEmptyFolders(path);\n            const _path = this._getFinalPath(cleanPath);\n            const url = new URL(this.url + `/object/upload/sign/${_path}`);\n            url.searchParams.set('token', token);\n            try {\n                let body;\n                const options = Object.assign({ upsert: DEFAULT_FILE_OPTIONS.upsert }, fileOptions);\n                const headers = Object.assign(Object.assign({}, this.headers), { 'x-upsert': String(options.upsert) });\n                if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append('cacheControl', options.cacheControl);\n                    body.append('', fileBody);\n                }\n                else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append('cacheControl', options.cacheControl);\n                }\n                else {\n                    body = fileBody;\n                    headers['cache-control'] = `max-age=${options.cacheControl}`;\n                    headers['content-type'] = options.contentType;\n                }\n                const res = yield this.fetch(url.toString(), {\n                    method: 'PUT',\n                    body: body,\n                    headers,\n                });\n                const data = yield res.json();\n                if (res.ok) {\n                    return {\n                        data: { path: cleanPath, fullPath: data.Key },\n                        error: null,\n                    };\n                }\n                else {\n                    const error = data;\n                    return { data: null, error };\n                }\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed upload URL.\n     * Signed upload URLs can be used to upload files to the bucket without further authentication.\n     * They are valid for 2 hours.\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n     */\n    createSignedUploadUrl(path, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let _path = this._getFinalPath(path);\n                const headers = Object.assign({}, this.headers);\n                if (options === null || options === void 0 ? void 0 : options.upsert) {\n                    headers['x-upsert'] = 'true';\n                }\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/upload/sign/${_path}`, {}, { headers });\n                const url = new URL(this.url + data.url);\n                const token = url.searchParams.get('token');\n                if (!token) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_1__.StorageError('No token returned by API');\n                }\n                return { data: { signedUrl: url.toString(), path, token }, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Replaces an existing file at the specified path with a new one.\n     *\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    update(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.uploadOrUpdate('PUT', path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Moves an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n     * @param options The destination options.\n     */\n    move(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/move`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Copies an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n     * @param options The destination options.\n     */\n    copy(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/copy`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket,\n                }, { headers: this.headers });\n                return { data: { path: data.Key }, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    createSignedUrl(path, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let _path = this._getFinalPath(path);\n                let data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/sign/${_path}`, Object.assign({ expiresIn }, ((options === null || options === void 0 ? void 0 : options.transform) ? { transform: options.transform } : {})), { headers: this.headers });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n                    ? `&download=${options.download === true ? '' : options.download}`\n                    : '';\n                const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`);\n                data = { signedUrl };\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n     * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     */\n    createSignedUrls(paths, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/sign/${this.bucketId}`, { expiresIn, paths }, { headers: this.headers });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n                    ? `&download=${options.download === true ? '' : options.download}`\n                    : '';\n                return {\n                    data: data.map((datum) => (Object.assign(Object.assign({}, datum), { signedUrl: datum.signedURL\n                            ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`)\n                            : null }))),\n                    error: null,\n                };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n     *\n     * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    download(path, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n            const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object';\n            const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n            const queryString = transformationQuery ? `?${transformationQuery}` : '';\n            try {\n                const _path = this._getFinalPath(path);\n                const res = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n                    headers: this.headers,\n                    noResolveJson: true,\n                });\n                const data = yield res.blob();\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing file.\n     * @param path\n     */\n    info(path) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const _path = this._getFinalPath(path);\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/object/info/${_path}`, {\n                    headers: this.headers,\n                });\n                return { data: (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_0__.recursiveToCamel)(data), error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Checks the existence of a file.\n     * @param path\n     */\n    exists(path) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const _path = this._getFinalPath(path);\n            try {\n                yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.head)(this.fetch, `${this.url}/object/${_path}`, {\n                    headers: this.headers,\n                });\n                return { data: true, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error) && error instanceof _lib_errors__WEBPACK_IMPORTED_MODULE_1__.StorageUnknownError) {\n                    const originalError = error.originalError;\n                    if ([400, 404].includes(originalError === null || originalError === void 0 ? void 0 : originalError.status)) {\n                        return { data: false, error };\n                    }\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n     * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n     *\n     * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n     * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    getPublicUrl(path, options) {\n        const _path = this._getFinalPath(path);\n        const _queryString = [];\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n            ? `download=${options.download === true ? '' : options.download}`\n            : '';\n        if (downloadQueryParam !== '') {\n            _queryString.push(downloadQueryParam);\n        }\n        const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n        const renderPath = wantsTransformation ? 'render/image' : 'object';\n        const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n        if (transformationQuery !== '') {\n            _queryString.push(transformationQuery);\n        }\n        let queryString = _queryString.join('&');\n        if (queryString !== '') {\n            queryString = `?${queryString}`;\n        }\n        return {\n            data: { publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`) },\n        };\n    }\n    /**\n     * Deletes files within the same bucket\n     *\n     * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n     */\n    remove(paths) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.remove)(this.fetch, `${this.url}/object/${this.bucketId}`, { prefixes: paths }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Get file metadata\n     * @param id the file id to retrieve metadata\n     */\n    // async getMetadata(\n    //   id: string\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Update file metadata\n     * @param id the file id to update metadata\n     * @param meta the new file metadata\n     */\n    // async updateMetadata(\n    //   id: string,\n    //   meta: Metadata\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await post(\n    //       this.fetch,\n    //       `${this.url}/metadata/${id}`,\n    //       { ...meta },\n    //       { headers: this.headers }\n    //     )\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Lists all the files within a bucket.\n     * @param path The folder path.\n     */\n    list(path, options, parameters) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const body = Object.assign(Object.assign(Object.assign({}, DEFAULT_SEARCH_OPTIONS), options), { prefix: path || '' });\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/list/${this.bucketId}`, body, { headers: this.headers }, parameters);\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    encodeMetadata(metadata) {\n        return JSON.stringify(metadata);\n    }\n    toBase64(data) {\n        if (typeof Buffer !== 'undefined') {\n            return Buffer.from(data).toString('base64');\n        }\n        return btoa(data);\n    }\n    _getFinalPath(path) {\n        return `${this.bucketId}/${path}`;\n    }\n    _removeEmptyFolders(path) {\n        return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/');\n    }\n    transformOptsToQueryString(transform) {\n        const params = [];\n        if (transform.width) {\n            params.push(`width=${transform.width}`);\n        }\n        if (transform.height) {\n            params.push(`height=${transform.height}`);\n        }\n        if (transform.resize) {\n            params.push(`resize=${transform.resize}`);\n        }\n        if (transform.format) {\n            params.push(`format=${transform.format}`);\n        }\n        if (transform.quality) {\n            params.push(`quality=${transform.quality}`);\n        }\n        return params.join('&');\n    }\n}\n//# sourceMappingURL=StorageFileApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js\n");

/***/ })

};
;