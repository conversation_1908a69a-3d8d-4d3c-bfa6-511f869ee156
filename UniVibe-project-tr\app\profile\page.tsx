"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Edit, LogOut, Calendar, MapPin } from "lucide-react"
import Link from "next/link"

// Mock user data
const userData = {
  name: "<PERSON>",
  email: "<EMAIL>",
  avatar: "/placeholder.svg?height=80&width=80",
  joinDate: "September 2023",
  eventsAttended: 12,
  favoriteCategories: ["Music", "Academic", "Sports"],
}

// Mock events data
const upcomingEvents = [
  {
    id: "1",
    title: "Spring Music Festival 2024",
    date: "March 15",
    location: "University Quad",
    category: "Music",
  },
  {
    id: "2",
    title: "Tech Talk: AI in Healthcare",
    date: "March 18",
    location: "Engineering Building",
    category: "Academic",
  },
]

const pastEvents = [
  {
    id: "3",
    title: "Winter Art Exhibition",
    date: "February 20",
    location: "Student Center",
    category: "Arts",
  },
  {
    id: "4",
    title: "Basketball Game vs State",
    date: "February 15",
    location: "Sports Complex",
    category: "Sports",
  },
  {
    id: "5",
    title: "Career Fair 2024",
    date: "February 10",
    location: "Main Hall",
    category: "Academic",
  },
]

export default function ProfilePage() {
  return (
    <div className="p-4 space-y-6">
      {/* Profile Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={userData.avatar || "/placeholder.svg"} />
              <AvatarFallback className="text-lg">JD</AvatarFallback>
            </Avatar>

            <div className="flex-1">
              <h2 className="text-xl font-bold">{userData.name}</h2>
              <p className="text-muted-foreground">{userData.email}</p>
              <p className="text-sm text-muted-foreground">Member since {userData.joinDate}</p>
            </div>

            <Button variant="outline" size="icon">
              <Edit className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-primary">{userData.eventsAttended}</p>
              <p className="text-sm text-muted-foreground">Events Attended</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-accent">{userData.favoriteCategories.length}</p>
              <p className="text-sm text-muted-foreground">Favorite Categories</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Favorite Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Favorite Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {userData.favoriteCategories.map((category) => (
              <Badge key={category} variant="secondary">
                {category}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Events Tabs */}
      <Tabs defaultValue="upcoming" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="upcoming">Upcoming Events</TabsTrigger>
          <TabsTrigger value="past">Past Events</TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming" className="space-y-3">
          {upcomingEvents.map((event) => (
            <Link key={event.id} href={`/event/${event.id}`}>
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{event.title}</h3>
                    <Badge variant="outline">{event.category}</Badge>
                  </div>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>{event.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>{event.location}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}

          {upcomingEvents.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No upcoming events</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="past" className="space-y-3">
          {pastEvents.map((event) => (
            <Card key={event.id} className="opacity-75">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold">{event.title}</h3>
                  <Badge variant="secondary">{event.category}</Badge>
                </div>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{event.date}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>{event.location}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>

      {/* Logout Button */}
      <Button variant="outline" className="w-full flex items-center gap-2 text-destructive hover:text-destructive">
        <LogOut className="h-4 w-4" />
        Logout
      </Button>
    </div>
  )
}
