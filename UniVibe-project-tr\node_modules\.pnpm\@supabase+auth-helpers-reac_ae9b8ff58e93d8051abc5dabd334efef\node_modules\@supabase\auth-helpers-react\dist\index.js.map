{"version": 3, "sources": ["../src/index.tsx", "../src/components/SessionContext.tsx"], "sourcesContent": ["// Types\nexport type { Session, User, SupabaseClient } from '@supabase/supabase-js';\n\n// Methods & Components\nexport * from './components/SessionContext';\n", "import { AuthError, Session, SupabaseClient } from '@supabase/supabase-js';\nimport React, {\n\tcreateContext,\n\tPropsWithChildren,\n\tuseContext,\n\tuseEffect,\n\tuseMemo,\n\tuseState\n} from 'react';\n\nexport type SessionContext =\n\t| {\n\t\t\tisLoading: true;\n\t\t\tsession: null;\n\t\t\terror: null;\n\t\t\tsupabaseClient: SupabaseClient;\n\t  }\n\t| {\n\t\t\tisLoading: false;\n\t\t\tsession: Session;\n\t\t\terror: null;\n\t\t\tsupabaseClient: SupabaseClient;\n\t  }\n\t| {\n\t\t\tisLoading: false;\n\t\t\tsession: null;\n\t\t\terror: AuthError;\n\t\t\tsupabaseClient: SupabaseClient;\n\t  }\n\t| {\n\t\t\tisLoading: false;\n\t\t\tsession: null;\n\t\t\terror: null;\n\t\t\tsupabaseClient: SupabaseClient;\n\t  };\n\nconst SessionContext = createContext<SessionContext>({\n\tisLoading: true,\n\tsession: null,\n\terror: null,\n\tsupabaseClient: {} as any\n});\n\nexport interface SessionContextProviderProps {\n\tsupabaseClient: SupabaseClient;\n\tinitialSession?: Session | null;\n}\n\nexport const SessionContextProvider = ({\n\tsupabaseClient,\n\tinitialSession = null,\n\tchildren\n}: PropsWithChildren<SessionContextProviderProps>) => {\n\tconst [session, setSession] = useState<Session | null>(initialSession);\n\tconst [isLoading, setIsLoading] = useState<boolean>(!initialSession);\n\tconst [error, setError] = useState<AuthError>();\n\n\tuseEffect(() => {\n\t\tif (!session && initialSession) {\n\t\t\tsetSession(initialSession);\n\t\t}\n\t}, [session, initialSession]);\n\n\tuseEffect(() => {\n\t\tlet mounted = true;\n\n\t\tasync function getSession() {\n\t\t\tconst {\n\t\t\t\tdata: { session },\n\t\t\t\terror\n\t\t\t} = await supabaseClient.auth.getSession();\n\n\t\t\t// only update the react state if the component is still mounted\n\t\t\tif (mounted) {\n\t\t\t\tif (error) {\n\t\t\t\t\tsetError(error);\n\t\t\t\t\tsetIsLoading(false);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tsetSession(session);\n\t\t\t\tsetIsLoading(false);\n\t\t\t}\n\t\t}\n\n\t\tgetSession();\n\n\t\treturn () => {\n\t\t\tmounted = false;\n\t\t};\n\t}, []);\n\n\tuseEffect(() => {\n\t\tconst {\n\t\t\tdata: { subscription }\n\t\t} = supabaseClient.auth.onAuthStateChange((event, session) => {\n\t\t\tif (\n\t\t\t\tsession &&\n\t\t\t\t(event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED')\n\t\t\t) {\n\t\t\t\tsetSession(session);\n\t\t\t}\n\n\t\t\tif (event === 'SIGNED_OUT') {\n\t\t\t\tsetSession(null);\n\t\t\t}\n\t\t});\n\n\t\treturn () => {\n\t\t\tsubscription.unsubscribe();\n\t\t};\n\t}, []);\n\n\tconst value: SessionContext = useMemo(() => {\n\t\tif (isLoading) {\n\t\t\treturn {\n\t\t\t\tisLoading: true,\n\t\t\t\tsession: null,\n\t\t\t\terror: null,\n\t\t\t\tsupabaseClient\n\t\t\t};\n\t\t}\n\n\t\tif (error) {\n\t\t\treturn {\n\t\t\t\tisLoading: false,\n\t\t\t\tsession: null,\n\t\t\t\terror,\n\t\t\t\tsupabaseClient\n\t\t\t};\n\t\t}\n\n\t\treturn {\n\t\t\tisLoading: false,\n\t\t\tsession,\n\t\t\terror: null,\n\t\t\tsupabaseClient\n\t\t};\n\t}, [isLoading, session, error]);\n\n\treturn <SessionContext.Provider value={value}>{children}</SessionContext.Provider>;\n};\n\nexport const useSessionContext = () => {\n\tconst context = useContext(SessionContext);\n\tif (context === undefined) {\n\t\tthrow new Error(`useSessionContext must be used within a SessionContextProvider.`);\n\t}\n\n\treturn context;\n};\n\nexport function useSupabaseClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database\n>() {\n\tconst context = useContext(SessionContext);\n\tif (context === undefined) {\n\t\tthrow new Error(`useSupabaseClient must be used within a SessionContextProvider.`);\n\t}\n\n\treturn context.supabaseClient as SupabaseClient<Database, SchemaName>;\n}\n\nexport const useSession = () => {\n\tconst context = useContext(SessionContext);\n\tif (context === undefined) {\n\t\tthrow new Error(`useSession must be used within a SessionContextProvider.`);\n\t}\n\n\treturn context.session;\n};\n\nexport const useUser = () => {\n\tconst context = useContext(SessionContext);\n\tif (context === undefined) {\n\t\tthrow new Error(`useUser must be used within a SessionContextProvider.`);\n\t}\n\n\treturn context.session?.user ?? null;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACCA,mBAOO;AAoIC;AAxGR,IAAM,qBAAiB,4BAA8B;AAAA,EACpD,WAAW;AAAA,EACX,SAAS;AAAA,EACT,OAAO;AAAA,EACP,gBAAgB,CAAC;AAClB,CAAC;AAOM,IAAM,yBAAyB,CAAC;AAAA,EACtC;AAAA,EACA,iBAAiB;AAAA,EACjB;AACD,MAAsD;AACrD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAyB,cAAc;AACrE,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAkB,CAAC,cAAc;AACnE,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAoB;AAE9C,8BAAU,MAAM;AACf,QAAI,CAAC,WAAW,gBAAgB;AAC/B,iBAAW,cAAc;AAAA,IAC1B;AAAA,EACD,GAAG,CAAC,SAAS,cAAc,CAAC;AAE5B,8BAAU,MAAM;AACf,QAAI,UAAU;AAEd,aAAe,aAAa;AAAA;AAC3B,cAAM;AAAA,UACL,MAAM,EAAE,SAAAA,SAAQ;AAAA,UAChB,OAAAC;AAAA,QACD,IAAI,MAAM,eAAe,KAAK,WAAW;AAGzC,YAAI,SAAS;AACZ,cAAIA,QAAO;AACV,qBAASA,MAAK;AACd,yBAAa,KAAK;AAClB;AAAA,UACD;AAEA,qBAAWD,QAAO;AAClB,uBAAa,KAAK;AAAA,QACnB;AAAA,MACD;AAAA;AAEA,eAAW;AAEX,WAAO,MAAM;AACZ,gBAAU;AAAA,IACX;AAAA,EACD,GAAG,CAAC,CAAC;AAEL,8BAAU,MAAM;AACf,UAAM;AAAA,MACL,MAAM,EAAE,aAAa;AAAA,IACtB,IAAI,eAAe,KAAK,kBAAkB,CAAC,OAAOA,aAAY;AAC7D,UACCA,aACC,UAAU,eAAe,UAAU,qBAAqB,UAAU,iBAClE;AACD,mBAAWA,QAAO;AAAA,MACnB;AAEA,UAAI,UAAU,cAAc;AAC3B,mBAAW,IAAI;AAAA,MAChB;AAAA,IACD,CAAC;AAED,WAAO,MAAM;AACZ,mBAAa,YAAY;AAAA,IAC1B;AAAA,EACD,GAAG,CAAC,CAAC;AAEL,QAAM,YAAwB,sBAAQ,MAAM;AAC3C,QAAI,WAAW;AACd,aAAO;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAEA,QAAI,OAAO;AACV,aAAO;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,MACN,WAAW;AAAA,MACX;AAAA,MACA,OAAO;AAAA,MACP;AAAA,IACD;AAAA,EACD,GAAG,CAAC,WAAW,SAAS,KAAK,CAAC;AAE9B,SAAO,4CAAC,eAAe,UAAf,EAAwB,OAAe,UAAS;AACzD;AAEO,IAAM,oBAAoB,MAAM;AACtC,QAAM,cAAU,yBAAW,cAAc;AACzC,MAAI,YAAY,QAAW;AAC1B,UAAM,IAAI,MAAM,iEAAiE;AAAA,EAClF;AAEA,SAAO;AACR;AAEO,SAAS,oBAKZ;AACH,QAAM,cAAU,yBAAW,cAAc;AACzC,MAAI,YAAY,QAAW;AAC1B,UAAM,IAAI,MAAM,iEAAiE;AAAA,EAClF;AAEA,SAAO,QAAQ;AAChB;AAEO,IAAM,aAAa,MAAM;AAC/B,QAAM,cAAU,yBAAW,cAAc;AACzC,MAAI,YAAY,QAAW;AAC1B,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC3E;AAEA,SAAO,QAAQ;AAChB;AAEO,IAAM,UAAU,MAAM;AA/K7B;AAgLC,QAAM,cAAU,yBAAW,cAAc;AACzC,MAAI,YAAY,QAAW;AAC1B,UAAM,IAAI,MAAM,uDAAuD;AAAA,EACxE;AAEA,UAAO,mBAAQ,YAAR,mBAAiB,SAAjB,YAAyB;AACjC;", "names": ["session", "error"]}