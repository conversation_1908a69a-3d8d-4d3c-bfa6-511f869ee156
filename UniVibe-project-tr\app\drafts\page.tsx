"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, Trash2, Send, FileText, Sparkles, Edit3, Eye, MoreVertical, Filter, Search, Plus, Archive, Zap } from "lucide-react"
import { useState, useEffect } from "react"
import { getDrafts, deleteDraft, publishDraft, cleanupDrafts } from "@/lib/events"
import { showEventCreatedNotification, showError } from "@/components/ui/notification"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu"

interface Draft {
  id: string
  title: string
  description: string
  date: string
  time: string
  location: string
  category: string
  organizer: string
  created_at: string
  updated_at: string
}

export default function DraftsPage() {
  const [drafts, setDrafts] = useState<Draft[]>([])
  const [filteredDrafts, setFilteredDrafts] = useState<Draft[]>([])
  const [publishingDrafts, setPublishingDrafts] = useState<Set<string>>(new Set())
  const [deletingDrafts, setDeletingDrafts] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [sortBy, setSortBy] = useState("updated")

  // Load drafts on component mount and when page becomes visible
  useEffect(() => {
    loadDrafts()

    // Add event listener for when the page becomes visible (user returns from edit page)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        loadDrafts()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Also listen for focus events
    const handleFocus = () => {
      loadDrafts()
    }

    window.addEventListener('focus', handleFocus)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
    }
  }, [])

  const loadDrafts = () => {
    // First clean up any corrupted data
    cleanupDrafts()

    const draftData = getDrafts()
    const draftArray = Object.values(draftData) as Draft[]

    // Deduplicate drafts by ID to prevent React key conflicts
    const uniqueDrafts = draftArray.reduce((acc, draft) => {
      // Only keep the draft if we haven't seen this ID before
      if (!acc.some(existingDraft => existingDraft.id === draft.id)) {
        acc.push(draft)
      }
      return acc
    }, [] as Draft[])

    // Debug logging (remove in production)
    if (process.env.NODE_ENV === 'development') {
      console.log('Raw draft data:', draftData)
      console.log('Draft array:', draftArray)
      console.log('Unique drafts:', uniqueDrafts)
      console.log('Draft IDs:', uniqueDrafts.map(d => d.id))
    }

    setDrafts(uniqueDrafts)
  }

  // Filter and sort drafts
  useEffect(() => {
    let filtered = [...drafts]

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(draft =>
        draft.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        draft.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        draft.location.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply category filter
    if (selectedCategory !== "All") {
      filtered = filtered.filter(draft => draft.category === selectedCategory)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "title":
          return a.title.localeCompare(b.title)
        case "created":
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        case "category":
          return a.category.localeCompare(b.category)
        default: // "updated"
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
      }
    })

    setFilteredDrafts(filtered)
  }, [drafts, searchQuery, selectedCategory, sortBy])

  // Get unique categories
  const categories = ["All", ...Array.from(new Set(drafts.map(draft => draft.category).filter(Boolean)))]

  const handlePublishDraft = async (draftId: string, draftTitle: string) => {
    setPublishingDrafts(prev => new Set(prev).add(draftId))

    try {
      const { data, error } = await publishDraft(draftId)

      if (error) {
        showError("Failed to publish draft", error)
        return
      }

      // Success!
      showEventCreatedNotification(draftTitle, false, data?.id)
      loadDrafts() // Refresh the drafts list
    } catch (error) {
      console.error("Error publishing draft:", error)
      showError("Something went wrong", "Please try again later")
    } finally {
      setPublishingDrafts(prev => {
        const newSet = new Set(prev)
        newSet.delete(draftId)
        return newSet
      })
    }
  }

  const handleDeleteDraft = async (draftId: string) => {
    setDeletingDrafts(prev => new Set(prev).add(draftId))

    try {
      const success = deleteDraft(draftId)
      if (success) {
        loadDrafts() // Refresh the drafts list
      } else {
        showError("Failed to delete draft", "Please try again")
      }
    } catch (error) {
      console.error("Error deleting draft:", error)
      showError("Something went wrong", "Please try again later")
    } finally {
      setDeletingDrafts(prev => {
        const newSet = new Set(prev)
        newSet.delete(draftId)
        return newSet
      })
    }
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return "No date set"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  const formatTime = (timeString: string) => {
    if (!timeString) return "No time set"
    const [hours, minutes] = timeString.split(':')
    const date = new Date()
    date.setHours(parseInt(hours), parseInt(minutes))
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Enhanced Header */}
        <div className="mb-12">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30 shadow-lg">
                  <Archive className="h-8 w-8 text-accent" />
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                  <span className="text-xs font-bold text-white">{drafts.length}</span>
                </div>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-white to-accent bg-clip-text text-transparent mb-2">
                  Draft Studio
                </h1>
                <p className="text-muted-foreground text-lg">
                  Craft, refine, and launch your events when the moment is perfect
                </p>
              </div>
            </div>

            <Button
              asChild
              size="lg"
              className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              <a href="/post" className="flex items-center gap-2">
                <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" />
                Create New Event
                <Sparkles className="h-4 w-4 opacity-70" />
              </a>
            </Button>
          </div>

          {/* Enhanced Search and Filter Bar */}
          <div className="mt-8 flex flex-col sm:flex-row gap-4 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-white/10 shadow-lg">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search drafts by title, description, or location..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-background/50 border-white/20 focus:border-accent/50 transition-colors"
              />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="bg-background/50 border-white/20 hover:bg-accent/10">
                  <Filter className="h-4 w-4 mr-2" />
                  {selectedCategory}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {categories.map((category) => (
                  <DropdownMenuItem
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={selectedCategory === category ? "bg-accent/10" : ""}
                  >
                    {category}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="bg-background/50 border-white/20 hover:bg-accent/10">
                  <MoreVertical className="h-4 w-4 mr-2" />
                  Sort
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => setSortBy("updated")} className={sortBy === "updated" ? "bg-accent/10" : ""}>
                  Last Updated
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSortBy("created")} className={sortBy === "created" ? "bg-accent/10" : ""}>
                  Date Created
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSortBy("title")} className={sortBy === "title" ? "bg-accent/10" : ""}>
                  Title A-Z
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSortBy("category")} className={sortBy === "category" ? "bg-accent/10" : ""}>
                  Category
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Stats Bar */}
          {drafts.length > 0 && (
            <div className="mt-6 flex flex-wrap gap-4 text-sm text-muted-foreground">
              <span className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                {filteredDrafts.length} of {drafts.length} drafts
              </span>
              {searchQuery && (
                <span className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  Filtered by "{searchQuery}"
                </span>
              )}
              {selectedCategory !== "All" && (
                <span className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Category: {selectedCategory}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Enhanced Drafts List */}
        {drafts.length === 0 ? (
          <Card className="shadow-2xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm">
            <CardContent className="p-16 text-center">
              <div className="relative mb-8">
                <div className="p-6 rounded-3xl bg-gradient-to-br from-accent/20 to-primary/20 w-fit mx-auto border border-accent/30">
                  <Archive className="h-12 w-12 text-accent" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-pulse">
                  <Plus className="h-4 w-4 text-white" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                Your Draft Studio Awaits
              </h3>
              <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto">
                Transform your event ideas into reality. Start crafting your first event and save it as a draft to perfect every detail.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  asChild
                  size="lg"
                  className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  <a href="/post" className="flex items-center gap-2">
                    <Zap className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                    Create Your First Event
                    <Sparkles className="h-4 w-4 opacity-70" />
                  </a>
                </Button>
                <Button variant="outline" size="lg" className="border-white/20 hover:bg-accent/10">
                  <Eye className="h-4 w-4 mr-2" />
                  Browse Examples
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : filteredDrafts.length === 0 ? (
          <Card className="shadow-xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm">
            <CardContent className="p-12 text-center">
              <div className="p-4 rounded-2xl bg-muted/20 w-fit mx-auto mb-6">
                <Search className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-2">No drafts match your search</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search terms or filters to find what you're looking for
              </p>
              <Button
                onClick={() => {
                  setSearchQuery("")
                  setSelectedCategory("All")
                }}
                variant="outline"
                className="border-white/20 hover:bg-accent/10"
              >
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-8">
            {filteredDrafts.map((draft, index) => (
              <Card
                key={draft.id}
                className="group shadow-xl border-white/10 hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm hover:scale-[1.02] hover:border-accent/30"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <CardHeader className="pb-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="p-2 rounded-lg bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30">
                          <FileText className="h-5 w-5 text-accent" />
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs bg-accent/10 border-accent/30 text-accent">
                            Draft
                          </Badge>
                          <div className="w-2 h-2 rounded-full bg-accent animate-pulse"></div>
                        </div>
                      </div>
                      <CardTitle className="text-2xl mb-3 group-hover:text-accent transition-colors duration-300">
                        {draft.title || "Untitled Event"}
                      </CardTitle>
                      {draft.category && (
                        <Badge variant="secondary" className="mb-2 bg-primary/10 text-primary border-primary/20">
                          {draft.category}
                        </Badge>
                      )}
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-accent/10"
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem asChild className="flex items-center gap-2">
                          <a href={`/drafts/edit/${draft.id}`}>
                            <Edit3 className="h-4 w-4" />
                            Edit Draft
                          </a>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          Preview
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="flex items-center gap-2 text-destructive focus:text-destructive"
                          onClick={() => handleDeleteDraft(draft.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete Draft
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Enhanced Description */}
                  {draft.description && (
                    <div className="p-4 rounded-xl bg-muted/20 border border-white/10">
                      <p className="text-muted-foreground line-clamp-3 leading-relaxed">
                        {draft.description}
                      </p>
                    </div>
                  )}

                  {/* Enhanced Event Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors">
                      <div className="p-2 rounded-lg bg-accent/10">
                        <Calendar className="h-4 w-4 text-accent" />
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground uppercase tracking-wide">Date</p>
                        <p className="font-medium">{formatDate(draft.date)}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors">
                      <div className="p-2 rounded-lg bg-primary/10">
                        <Clock className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground uppercase tracking-wide">Time</p>
                        <p className="font-medium">{formatTime(draft.time)}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors">
                      <div className="p-2 rounded-lg bg-secondary/10">
                        <MapPin className="h-4 w-4 text-secondary-foreground" />
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground uppercase tracking-wide">Location</p>
                        <p className="font-medium">{draft.location || "No location set"}</p>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Draft Metadata */}
                  <div className="flex items-center justify-between text-xs text-muted-foreground bg-muted/10 rounded-lg p-3 border border-white/10">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      <span>Created {new Date(draft.created_at).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                      <span>Updated {new Date(draft.updated_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Enhanced Actions */}
                  <div className="flex gap-3 pt-2">
                    <Button
                      onClick={() => handlePublishDraft(draft.id, draft.title)}
                      disabled={publishingDrafts.has(draft.id)}
                      size="lg"
                      className="flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group"
                    >
                      {publishingDrafts.has(draft.id) ? (
                        <>
                          <Sparkles className="h-5 w-5 mr-2 animate-spin" />
                          Publishing...
                        </>
                      ) : (
                        <>
                          <Zap className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                          🚀 Launch Event
                          <Sparkles className="h-4 w-4 ml-2 opacity-70" />
                        </>
                      )}
                    </Button>
                    <Button
                      asChild
                      variant="outline"
                      size="lg"
                      className="border-white/20 hover:bg-accent/10 hover:border-accent/30 transition-all duration-300"
                    >
                      <a href={`/drafts/edit/${draft.id}`}>
                        <Edit3 className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
