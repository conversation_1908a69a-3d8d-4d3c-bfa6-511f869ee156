"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
  allowedRoles?: ('student' | 'organizer')[]
}

export function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo = '/auth/login',
  allowedRoles 
}: AuthGuardProps) {
  const { user, profile, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (loading) return // Wait for auth to load

    // If auth is required but user is not logged in
    if (requireAuth && !user) {
      const currentPath = window.location.pathname
      const redirectUrl = `${redirectTo}?redirectTo=${encodeURIComponent(currentPath)}`
      router.push(redirectUrl)
      return
    }

    // If specific roles are required, check user role
    if (allowedRoles && user && profile) {
      if (!allowedRoles.includes(profile.role as 'student' | 'organizer')) {
        router.push('/') // Redirect to home if role not allowed
        return
      }
    }
  }, [user, profile, loading, requireAuth, allowedRoles, redirectTo, router])

  // Show loading while checking auth
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent mx-auto"></div>
          <h2 className="text-xl font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Checking authentication</p>
        </div>
      </div>
    )
  }

  // Don't render children if auth is required but user is not logged in
  if (requireAuth && !user) {
    return null
  }

  // Don't render children if role is required but user doesn't have it
  if (allowedRoles && user && profile && !allowedRoles.includes(profile.role as 'student' | 'organizer')) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="p-8 rounded-3xl bg-gradient-to-br from-red-500/20 to-orange-500/20 w-fit mx-auto border border-red-500/30 backdrop-blur-sm">
            <svg className="h-16 w-16 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold">Access Denied</h2>
          <p className="text-muted-foreground max-w-md mx-auto">
            You don't have permission to access this page. This page is restricted to {allowedRoles?.join(' and ')} users only.
          </p>
          <button
            onClick={() => router.push('/')}
            className="px-6 py-3 bg-gradient-to-r from-primary to-accent text-white rounded-lg hover:from-primary/90 hover:to-accent/90 transition-all"
          >
            Go to Home
          </button>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
