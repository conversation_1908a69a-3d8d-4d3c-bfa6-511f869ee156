"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+realtime-js@2.11.2";
exports.ids = ["vendor-chunks/@supabase+realtime-js@2.11.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* binding */ REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* binding */ REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* binding */ REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* binding */ REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   \"default\": () => (/* binding */ RealtimeChannel)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_push__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/push */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/push.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/timer */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RealtimePresence */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n\n\n\n\n\n\nvar REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nvar REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n    REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n    REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n    REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n    REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nvar REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n    REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n    REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n    REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n    REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nconst REALTIME_CHANNEL_STATES = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nclass RealtimeChannel {\n    constructor(\n    /** Topic name can be any string. */\n    topic, params = { config: {} }, socket) {\n        this.topic = topic;\n        this.params = params;\n        this.socket = socket;\n        this.bindings = {};\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n        this.joinedOnce = false;\n        this.pushBuffer = [];\n        this.subTopic = topic.replace(/^realtime:/i, '');\n        this.params.config = Object.assign({\n            broadcast: { ack: false, self: false },\n            presence: { key: '' },\n            private: false,\n        }, params.config);\n        this.timeout = this.socket.timeout;\n        this.joinPush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.join, this.params, this.timeout);\n        this.rejoinTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_2__[\"default\"](() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n        this.joinPush.receive('ok', () => {\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n            this.rejoinTimer.reset();\n            this.pushBuffer.forEach((pushEvent) => pushEvent.send());\n            this.pushBuffer = [];\n        });\n        this._onClose(() => {\n            this.rejoinTimer.reset();\n            this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n            this.socket._remove(this);\n        });\n        this._onError((reason) => {\n            if (this._isLeaving() || this._isClosed()) {\n                return;\n            }\n            this.socket.log('channel', `error ${this.topic}`, reason);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this.joinPush.receive('timeout', () => {\n            if (!this._isJoining()) {\n                return;\n            }\n            this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n            this._trigger(this._replyEventName(ref), payload);\n        });\n        this.presence = new _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__[\"default\"](this);\n        this.broadcastEndpointURL =\n            (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_4__.httpEndpointURL)(this.socket.endPoint) + '/api/broadcast';\n        this.private = this.params.config.private || false;\n    }\n    /** Subscribe registers your client with the server */\n    subscribe(callback, timeout = this.timeout) {\n        var _a, _b;\n        if (!this.socket.isConnected()) {\n            this.socket.connect();\n        }\n        if (this.joinedOnce) {\n            throw `tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance`;\n        }\n        else {\n            const { config: { broadcast, presence, private: isPrivate }, } = this.params;\n            this._onError((e) => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n            this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n            const accessTokenPayload = {};\n            const config = {\n                broadcast,\n                presence,\n                postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map((r) => r.filter)) !== null && _b !== void 0 ? _b : [],\n                private: isPrivate,\n            };\n            if (this.socket.accessTokenValue) {\n                accessTokenPayload.access_token = this.socket.accessTokenValue;\n            }\n            this.updateJoinPayload(Object.assign({ config }, accessTokenPayload));\n            this.joinedOnce = true;\n            this._rejoin(timeout);\n            this.joinPush\n                .receive('ok', async ({ postgres_changes }) => {\n                var _a;\n                this.socket.setAuth();\n                if (postgres_changes === undefined) {\n                    callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n                else {\n                    const clientPostgresBindings = this.bindings.postgres_changes;\n                    const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n                    const newPostgresBindings = [];\n                    for (let i = 0; i < bindingsLen; i++) {\n                        const clientPostgresBinding = clientPostgresBindings[i];\n                        const { filter: { event, schema, table, filter }, } = clientPostgresBinding;\n                        const serverPostgresFilter = postgres_changes && postgres_changes[i];\n                        if (serverPostgresFilter &&\n                            serverPostgresFilter.event === event &&\n                            serverPostgresFilter.schema === schema &&\n                            serverPostgresFilter.table === table &&\n                            serverPostgresFilter.filter === filter) {\n                            newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), { id: serverPostgresFilter.id }));\n                        }\n                        else {\n                            this.unsubscribe();\n                            callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n                            return;\n                        }\n                    }\n                    this.bindings.postgres_changes = newPostgresBindings;\n                    callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n            })\n                .receive('error', (error) => {\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n                return;\n            })\n                .receive('timeout', () => {\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n                return;\n            });\n        }\n        return this;\n    }\n    presenceState() {\n        return this.presence.state;\n    }\n    async track(payload, opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'track',\n            payload,\n        }, opts.timeout || this.timeout);\n    }\n    async untrack(opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'untrack',\n        }, opts);\n    }\n    on(type, filter, callback) {\n        return this._on(type, filter, callback);\n    }\n    /**\n     * Sends a message into the channel.\n     *\n     * @param args Arguments to send to channel\n     * @param args.type The type of event to send\n     * @param args.event The name of the event being sent\n     * @param args.payload Payload to be sent\n     * @param opts Options to be used during the send process\n     */\n    async send(args, opts = {}) {\n        var _a, _b;\n        if (!this._canPush() && args.type === 'broadcast') {\n            const { event, payload: endpoint_payload } = args;\n            const authorization = this.socket.accessTokenValue\n                ? `Bearer ${this.socket.accessTokenValue}`\n                : '';\n            const options = {\n                method: 'POST',\n                headers: {\n                    Authorization: authorization,\n                    apikey: this.socket.apiKey ? this.socket.apiKey : '',\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    messages: [\n                        {\n                            topic: this.subTopic,\n                            event,\n                            payload: endpoint_payload,\n                            private: this.private,\n                        },\n                    ],\n                }),\n            };\n            try {\n                const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n                await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n                return response.ok ? 'ok' : 'error';\n            }\n            catch (error) {\n                if (error.name === 'AbortError') {\n                    return 'timed out';\n                }\n                else {\n                    return 'error';\n                }\n            }\n        }\n        else {\n            return new Promise((resolve) => {\n                var _a, _b, _c;\n                const push = this._push(args.type, args, opts.timeout || this.timeout);\n                if (args.type === 'broadcast' && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n                    resolve('ok');\n                }\n                push.receive('ok', () => resolve('ok'));\n                push.receive('error', () => resolve('error'));\n                push.receive('timeout', () => resolve('timed out'));\n            });\n        }\n    }\n    updateJoinPayload(payload) {\n        this.joinPush.updatePayload(payload);\n    }\n    /**\n     * Leaves the channel.\n     *\n     * Unsubscribes from server events, and instructs channel to terminate on server.\n     * Triggers onClose() hooks.\n     *\n     * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n     * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n     */\n    unsubscribe(timeout = this.timeout) {\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n        const onClose = () => {\n            this.socket.log('channel', `leave ${this.topic}`);\n            this._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, 'leave', this._joinRef());\n        };\n        this.rejoinTimer.reset();\n        // Destroy joinPush to avoid connection timeouts during unscription phase\n        this.joinPush.destroy();\n        return new Promise((resolve) => {\n            const leavePush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.leave, {}, timeout);\n            leavePush\n                .receive('ok', () => {\n                onClose();\n                resolve('ok');\n            })\n                .receive('timeout', () => {\n                onClose();\n                resolve('timed out');\n            })\n                .receive('error', () => {\n                resolve('error');\n            });\n            leavePush.send();\n            if (!this._canPush()) {\n                leavePush.trigger('ok', {});\n            }\n        });\n    }\n    /** @internal */\n    async _fetchWithTimeout(url, options, timeout) {\n        const controller = new AbortController();\n        const id = setTimeout(() => controller.abort(), timeout);\n        const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), { signal: controller.signal }));\n        clearTimeout(id);\n        return response;\n    }\n    /** @internal */\n    _push(event, payload, timeout = this.timeout) {\n        if (!this.joinedOnce) {\n            throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n        }\n        let pushEvent = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, event, payload, timeout);\n        if (this._canPush()) {\n            pushEvent.send();\n        }\n        else {\n            pushEvent.startTimeout();\n            this.pushBuffer.push(pushEvent);\n        }\n        return pushEvent;\n    }\n    /**\n     * Overridable message hook\n     *\n     * Receives all events for specialized message handling before dispatching to the channel callbacks.\n     * Must return the payload, modified or unmodified.\n     *\n     * @internal\n     */\n    _onMessage(_event, payload, _ref) {\n        return payload;\n    }\n    /** @internal */\n    _isMember(topic) {\n        return this.topic === topic;\n    }\n    /** @internal */\n    _joinRef() {\n        return this.joinPush.ref;\n    }\n    /** @internal */\n    _trigger(type, payload, ref) {\n        var _a, _b;\n        const typeLower = type.toLocaleLowerCase();\n        const { close, error, leave, join } = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS;\n        const events = [close, error, leave, join];\n        if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n            return;\n        }\n        let handledPayload = this._onMessage(typeLower, payload, ref);\n        if (payload && !handledPayload) {\n            throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n        }\n        if (['insert', 'update', 'delete'].includes(typeLower)) {\n            (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter((bind) => {\n                var _a, _b, _c;\n                return (((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' ||\n                    ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower);\n            }).map((bind) => bind.callback(handledPayload, ref));\n        }\n        else {\n            (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter((bind) => {\n                var _a, _b, _c, _d, _e, _f;\n                if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n                    if ('id' in bind) {\n                        const bindId = bind.id;\n                        const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n                        return (bindId &&\n                            ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) &&\n                            (bindEvent === '*' ||\n                                (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) ===\n                                    ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase())));\n                    }\n                    else {\n                        const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n                        return (bindEvent === '*' ||\n                            bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase()));\n                    }\n                }\n                else {\n                    return bind.type.toLocaleLowerCase() === typeLower;\n                }\n            }).map((bind) => {\n                if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n                    const postgresChanges = handledPayload.data;\n                    const { schema, table, commit_timestamp, type, errors } = postgresChanges;\n                    const enrichedPayload = {\n                        schema: schema,\n                        table: table,\n                        commit_timestamp: commit_timestamp,\n                        eventType: type,\n                        new: {},\n                        old: {},\n                        errors: errors,\n                    };\n                    handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n                }\n                bind.callback(handledPayload, ref);\n            });\n        }\n    }\n    /** @internal */\n    _isClosed() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n    }\n    /** @internal */\n    _isJoined() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n    }\n    /** @internal */\n    _isJoining() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n    }\n    /** @internal */\n    _isLeaving() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n    }\n    /** @internal */\n    _replyEventName(ref) {\n        return `chan_reply_${ref}`;\n    }\n    /** @internal */\n    _on(type, filter, callback) {\n        const typeLower = type.toLocaleLowerCase();\n        const binding = {\n            type: typeLower,\n            filter: filter,\n            callback: callback,\n        };\n        if (this.bindings[typeLower]) {\n            this.bindings[typeLower].push(binding);\n        }\n        else {\n            this.bindings[typeLower] = [binding];\n        }\n        return this;\n    }\n    /** @internal */\n    _off(type, filter) {\n        const typeLower = type.toLocaleLowerCase();\n        this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n            var _a;\n            return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower &&\n                RealtimeChannel.isEqual(bind.filter, filter));\n        });\n        return this;\n    }\n    /** @internal */\n    static isEqual(obj1, obj2) {\n        if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n            return false;\n        }\n        for (const k in obj1) {\n            if (obj1[k] !== obj2[k]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /** @internal */\n    _rejoinUntilConnected() {\n        this.rejoinTimer.scheduleTimeout();\n        if (this.socket.isConnected()) {\n            this._rejoin();\n        }\n    }\n    /**\n     * Registers a callback that will be executed when the channel closes.\n     *\n     * @internal\n     */\n    _onClose(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, {}, callback);\n    }\n    /**\n     * Registers a callback that will be executed when the channel encounteres an error.\n     *\n     * @internal\n     */\n    _onError(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.error, {}, (reason) => callback(reason));\n    }\n    /**\n     * Returns `true` if the socket is connected and the channel has been joined.\n     *\n     * @internal\n     */\n    _canPush() {\n        return this.socket.isConnected() && this._isJoined();\n    }\n    /** @internal */\n    _rejoin(timeout = this.timeout) {\n        if (this._isLeaving()) {\n            return;\n        }\n        this.socket._leaveOpenTopic(this.topic);\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n        this.joinPush.resend(timeout);\n    }\n    /** @internal */\n    _getPayloadRecords(payload) {\n        const records = {\n            new: {},\n            old: {},\n        };\n        if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n            records.new = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.record);\n        }\n        if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n            records.old = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.old_record);\n        }\n        return records;\n    }\n}\n//# sourceMappingURL=RealtimeChannel.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealtimeClient)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_serializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/serializer */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/timer */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n\n\n\n\n\nconst noop = () => { };\nconst NATIVE_WEBSOCKET_AVAILABLE = typeof WebSocket !== 'undefined';\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nclass RealtimeClient {\n    /**\n     * Initializes the Socket.\n     *\n     * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n     * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n     * @param options.transport The Websocket Transport, for example WebSocket.\n     * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n     * @param options.params The optional params to pass when connecting.\n     * @param options.headers The optional headers to pass when connecting.\n     * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n     * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n     * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n     * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n     * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n     * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n     * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n     */\n    constructor(endPoint, options) {\n        var _a;\n        this.accessTokenValue = null;\n        this.apiKey = null;\n        this.channels = [];\n        this.endPoint = '';\n        this.httpEndpoint = '';\n        this.headers = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_HEADERS;\n        this.params = {};\n        this.timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TIMEOUT;\n        this.heartbeatIntervalMs = 30000;\n        this.heartbeatTimer = undefined;\n        this.pendingHeartbeatRef = null;\n        this.ref = 0;\n        this.logger = noop;\n        this.conn = null;\n        this.sendBuffer = [];\n        this.serializer = new _lib_serializer__WEBPACK_IMPORTED_MODULE_1__[\"default\"]();\n        this.stateChangeCallbacks = {\n            open: [],\n            close: [],\n            error: [],\n            message: [],\n        };\n        this.accessToken = null;\n        /**\n         * Use either custom fetch, if provided, or default fetch to make HTTP requests\n         *\n         * @internal\n         */\n        this._resolveFetch = (customFetch) => {\n            let _fetch;\n            if (customFetch) {\n                _fetch = customFetch;\n            }\n            else if (typeof fetch === 'undefined') {\n                _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n            }\n            else {\n                _fetch = fetch;\n            }\n            return (...args) => _fetch(...args);\n        };\n        this.endPoint = `${endPoint}/${_lib_constants__WEBPACK_IMPORTED_MODULE_0__.TRANSPORTS.websocket}`;\n        this.httpEndpoint = (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_3__.httpEndpointURL)(endPoint);\n        if (options === null || options === void 0 ? void 0 : options.transport) {\n            this.transport = options.transport;\n        }\n        else {\n            this.transport = null;\n        }\n        if (options === null || options === void 0 ? void 0 : options.params)\n            this.params = options.params;\n        if (options === null || options === void 0 ? void 0 : options.headers)\n            this.headers = Object.assign(Object.assign({}, this.headers), options.headers);\n        if (options === null || options === void 0 ? void 0 : options.timeout)\n            this.timeout = options.timeout;\n        if (options === null || options === void 0 ? void 0 : options.logger)\n            this.logger = options.logger;\n        if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs)\n            this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n        const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n        if (accessTokenValue) {\n            this.accessTokenValue = accessTokenValue;\n            this.apiKey = accessTokenValue;\n        }\n        this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs)\n            ? options.reconnectAfterMs\n            : (tries) => {\n                return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n            };\n        this.encode = (options === null || options === void 0 ? void 0 : options.encode)\n            ? options.encode\n            : (payload, callback) => {\n                return callback(JSON.stringify(payload));\n            };\n        this.decode = (options === null || options === void 0 ? void 0 : options.decode)\n            ? options.decode\n            : this.serializer.decode.bind(this.serializer);\n        this.reconnectTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_2__[\"default\"](async () => {\n            this.disconnect();\n            this.connect();\n        }, this.reconnectAfterMs);\n        this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n        if (options === null || options === void 0 ? void 0 : options.worker) {\n            if (typeof window !== 'undefined' && !window.Worker) {\n                throw new Error('Web Worker is not supported');\n            }\n            this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n            this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n        }\n        this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n    }\n    /**\n     * Connects the socket, unless already connected.\n     */\n    connect() {\n        if (this.conn) {\n            return;\n        }\n        if (this.transport) {\n            this.conn = new this.transport(this.endpointURL(), undefined, {\n                headers: this.headers,\n            });\n            return;\n        }\n        if (NATIVE_WEBSOCKET_AVAILABLE) {\n            this.conn = new WebSocket(this.endpointURL());\n            this.setupConnection();\n            return;\n        }\n        this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n            close: () => {\n                this.conn = null;\n            },\n        });\n        Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/ws@8.18.2\"), __webpack_require__.e(\"_8bfc-_cf7b\")]).then(__webpack_require__.bind(__webpack_require__, /*! ws */ \"(ssr)/./node_modules/.pnpm/ws@8.18.2/node_modules/ws/wrapper.mjs\")).then(({ default: WS }) => {\n            this.conn = new WS(this.endpointURL(), undefined, {\n                headers: this.headers,\n            });\n            this.setupConnection();\n        });\n    }\n    /**\n     * Returns the URL of the websocket.\n     * @returns string The URL of the websocket.\n     */\n    endpointURL() {\n        return this._appendParams(this.endPoint, Object.assign({}, this.params, { vsn: _lib_constants__WEBPACK_IMPORTED_MODULE_0__.VSN }));\n    }\n    /**\n     * Disconnects the socket.\n     *\n     * @param code A numeric status code to send on disconnect.\n     * @param reason A custom reason for the disconnect.\n     */\n    disconnect(code, reason) {\n        if (this.conn) {\n            this.conn.onclose = function () { }; // noop\n            if (code) {\n                this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n            }\n            else {\n                this.conn.close();\n            }\n            this.conn = null;\n            // remove open handles\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.reconnectTimer.reset();\n        }\n    }\n    /**\n     * Returns all created channels\n     */\n    getChannels() {\n        return this.channels;\n    }\n    /**\n     * Unsubscribes and removes a single channel\n     * @param channel A RealtimeChannel instance\n     */\n    async removeChannel(channel) {\n        const status = await channel.unsubscribe();\n        if (this.channels.length === 0) {\n            this.disconnect();\n        }\n        return status;\n    }\n    /**\n     * Unsubscribes and removes all channels\n     */\n    async removeAllChannels() {\n        const values_1 = await Promise.all(this.channels.map((channel) => channel.unsubscribe()));\n        this.disconnect();\n        return values_1;\n    }\n    /**\n     * Logs the message.\n     *\n     * For customized logging, `this.logger` can be overridden.\n     */\n    log(kind, msg, data) {\n        this.logger(kind, msg, data);\n    }\n    /**\n     * Returns the current state of the socket.\n     */\n    connectionState() {\n        switch (this.conn && this.conn.readyState) {\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.connecting:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Connecting;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.open:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Open;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.closing:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Closing;\n            default:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Closed;\n        }\n    }\n    /**\n     * Returns `true` is the connection is open.\n     */\n    isConnected() {\n        return this.connectionState() === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Open;\n    }\n    channel(topic, params = { config: {} }) {\n        const chan = new _RealtimeChannel__WEBPACK_IMPORTED_MODULE_4__[\"default\"](`realtime:${topic}`, params, this);\n        this.channels.push(chan);\n        return chan;\n    }\n    /**\n     * Push out a message if the socket is connected.\n     *\n     * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n     */\n    push(data) {\n        const { topic, event, payload, ref } = data;\n        const callback = () => {\n            this.encode(data, (result) => {\n                var _a;\n                (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n            });\n        };\n        this.log('push', `${topic} ${event} (${ref})`, payload);\n        if (this.isConnected()) {\n            callback();\n        }\n        else {\n            this.sendBuffer.push(callback);\n        }\n    }\n    /**\n     * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n     *\n     * If param is null it will use the `accessToken` callback function or the token set on the client.\n     *\n     * On callback used, it will set the value of the token internal to the client.\n     *\n     * @param token A JWT string to override the token set on the client.\n     */\n    async setAuth(token = null) {\n        let tokenToSend = token ||\n            (this.accessToken && (await this.accessToken())) ||\n            this.accessTokenValue;\n        if (tokenToSend) {\n            let parsed = null;\n            try {\n                parsed = JSON.parse(atob(tokenToSend.split('.')[1]));\n            }\n            catch (_error) { }\n            if (parsed && parsed.exp) {\n                let now = Math.floor(Date.now() / 1000);\n                let valid = now - parsed.exp < 0;\n                if (!valid) {\n                    this.log('auth', `InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n                    return Promise.reject(`InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n                }\n            }\n            this.accessTokenValue = tokenToSend;\n            this.channels.forEach((channel) => {\n                tokenToSend && channel.updateJoinPayload({ access_token: tokenToSend });\n                if (channel.joinedOnce && channel._isJoined()) {\n                    channel._push(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.access_token, {\n                        access_token: tokenToSend,\n                    });\n                }\n            });\n        }\n    }\n    /**\n     * Sends a heartbeat message if the socket is connected.\n     */\n    async sendHeartbeat() {\n        var _a;\n        if (!this.isConnected()) {\n            return;\n        }\n        if (this.pendingHeartbeatRef) {\n            this.pendingHeartbeatRef = null;\n            this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n            (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.WS_CLOSE_NORMAL, 'hearbeat timeout');\n            return;\n        }\n        this.pendingHeartbeatRef = this._makeRef();\n        this.push({\n            topic: 'phoenix',\n            event: 'heartbeat',\n            payload: {},\n            ref: this.pendingHeartbeatRef,\n        });\n        this.setAuth();\n    }\n    /**\n     * Flushes send buffer\n     */\n    flushSendBuffer() {\n        if (this.isConnected() && this.sendBuffer.length > 0) {\n            this.sendBuffer.forEach((callback) => callback());\n            this.sendBuffer = [];\n        }\n    }\n    /**\n     * Return the next message ref, accounting for overflows\n     *\n     * @internal\n     */\n    _makeRef() {\n        let newRef = this.ref + 1;\n        if (newRef === this.ref) {\n            this.ref = 0;\n        }\n        else {\n            this.ref = newRef;\n        }\n        return this.ref.toString();\n    }\n    /**\n     * Unsubscribe from channels with the specified topic.\n     *\n     * @internal\n     */\n    _leaveOpenTopic(topic) {\n        let dupChannel = this.channels.find((c) => c.topic === topic && (c._isJoined() || c._isJoining()));\n        if (dupChannel) {\n            this.log('transport', `leaving duplicate topic \"${topic}\"`);\n            dupChannel.unsubscribe();\n        }\n    }\n    /**\n     * Removes a subscription from the socket.\n     *\n     * @param channel An open subscription.\n     *\n     * @internal\n     */\n    _remove(channel) {\n        this.channels = this.channels.filter((c) => c._joinRef() !== channel._joinRef());\n    }\n    /**\n     * Sets up connection handlers.\n     *\n     * @internal\n     */\n    setupConnection() {\n        if (this.conn) {\n            this.conn.binaryType = 'arraybuffer';\n            this.conn.onopen = () => this._onConnOpen();\n            this.conn.onerror = (error) => this._onConnError(error);\n            this.conn.onmessage = (event) => this._onConnMessage(event);\n            this.conn.onclose = (event) => this._onConnClose(event);\n        }\n    }\n    /** @internal */\n    _onConnMessage(rawMessage) {\n        this.decode(rawMessage.data, (msg) => {\n            let { topic, event, payload, ref } = msg;\n            if (ref && ref === this.pendingHeartbeatRef) {\n                this.pendingHeartbeatRef = null;\n            }\n            this.log('receive', `${payload.status || ''} ${topic} ${event} ${(ref && '(' + ref + ')') || ''}`, payload);\n            this.channels\n                .filter((channel) => channel._isMember(topic))\n                .forEach((channel) => channel._trigger(event, payload, ref));\n            this.stateChangeCallbacks.message.forEach((callback) => callback(msg));\n        });\n    }\n    /** @internal */\n    async _onConnOpen() {\n        this.log('transport', `connected to ${this.endpointURL()}`);\n        this.flushSendBuffer();\n        this.reconnectTimer.reset();\n        if (!this.worker) {\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n        }\n        else {\n            if (this.workerUrl) {\n                this.log('worker', `starting worker for from ${this.workerUrl}`);\n            }\n            else {\n                this.log('worker', `starting default worker`);\n            }\n            const objectUrl = this._workerObjectUrl(this.workerUrl);\n            this.workerRef = new Worker(objectUrl);\n            this.workerRef.onerror = (error) => {\n                this.log('worker', 'worker error', error.message);\n                this.workerRef.terminate();\n            };\n            this.workerRef.onmessage = (event) => {\n                if (event.data.event === 'keepAlive') {\n                    this.sendHeartbeat();\n                }\n            };\n            this.workerRef.postMessage({\n                event: 'start',\n                interval: this.heartbeatIntervalMs,\n            });\n        }\n        this.stateChangeCallbacks.open.forEach((callback) => callback());\n    }\n    /** @internal */\n    _onConnClose(event) {\n        this.log('transport', 'close', event);\n        this._triggerChanError();\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.reconnectTimer.scheduleTimeout();\n        this.stateChangeCallbacks.close.forEach((callback) => callback(event));\n    }\n    /** @internal */\n    _onConnError(error) {\n        this.log('transport', error.message);\n        this._triggerChanError();\n        this.stateChangeCallbacks.error.forEach((callback) => callback(error));\n    }\n    /** @internal */\n    _triggerChanError() {\n        this.channels.forEach((channel) => channel._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.error));\n    }\n    /** @internal */\n    _appendParams(url, params) {\n        if (Object.keys(params).length === 0) {\n            return url;\n        }\n        const prefix = url.match(/\\?/) ? '&' : '?';\n        const query = new URLSearchParams(params);\n        return `${url}${prefix}${query}`;\n    }\n    _workerObjectUrl(url) {\n        let result_url;\n        if (url) {\n            result_url = url;\n        }\n        else {\n            const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' });\n            result_url = URL.createObjectURL(blob);\n        }\n        return result_url;\n    }\n}\nclass WSWebSocketDummy {\n    constructor(address, _protocols, options) {\n        this.binaryType = 'arraybuffer';\n        this.onclose = () => { };\n        this.onerror = () => { };\n        this.onmessage = () => { };\n        this.onopen = () => { };\n        this.readyState = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.connecting;\n        this.send = () => { };\n        this.url = null;\n        this.url = address;\n        this.close = options.close;\n    }\n}\n//# sourceMappingURL=RealtimeClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* binding */ REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   \"default\": () => (/* binding */ RealtimePresence)\n/* harmony export */ });\n/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nvar REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nclass RealtimePresence {\n    /**\n     * Initializes the Presence.\n     *\n     * @param channel - The RealtimeChannel\n     * @param opts - The options,\n     *        for example `{events: {state: 'state', diff: 'diff'}}`\n     */\n    constructor(channel, opts) {\n        this.channel = channel;\n        this.state = {};\n        this.pendingDiffs = [];\n        this.joinRef = null;\n        this.caller = {\n            onJoin: () => { },\n            onLeave: () => { },\n            onSync: () => { },\n        };\n        const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n            state: 'presence_state',\n            diff: 'presence_diff',\n        };\n        this.channel._on(events.state, {}, (newState) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            this.joinRef = this.channel._joinRef();\n            this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n            this.pendingDiffs.forEach((diff) => {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n            });\n            this.pendingDiffs = [];\n            onSync();\n        });\n        this.channel._on(events.diff, {}, (diff) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            if (this.inPendingSyncState()) {\n                this.pendingDiffs.push(diff);\n            }\n            else {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n                onSync();\n            }\n        });\n        this.onJoin((key, currentPresences, newPresences) => {\n            this.channel._trigger('presence', {\n                event: 'join',\n                key,\n                currentPresences,\n                newPresences,\n            });\n        });\n        this.onLeave((key, currentPresences, leftPresences) => {\n            this.channel._trigger('presence', {\n                event: 'leave',\n                key,\n                currentPresences,\n                leftPresences,\n            });\n        });\n        this.onSync(() => {\n            this.channel._trigger('presence', { event: 'sync' });\n        });\n    }\n    /**\n     * Used to sync the list of presences on the server with the\n     * client's state.\n     *\n     * An optional `onJoin` and `onLeave` callback can be provided to\n     * react to changes in the client's local presences across\n     * disconnects and reconnects with the server.\n     *\n     * @internal\n     */\n    static syncState(currentState, newState, onJoin, onLeave) {\n        const state = this.cloneDeep(currentState);\n        const transformedState = this.transformState(newState);\n        const joins = {};\n        const leaves = {};\n        this.map(state, (key, presences) => {\n            if (!transformedState[key]) {\n                leaves[key] = presences;\n            }\n        });\n        this.map(transformedState, (key, newPresences) => {\n            const currentPresences = state[key];\n            if (currentPresences) {\n                const newPresenceRefs = newPresences.map((m) => m.presence_ref);\n                const curPresenceRefs = currentPresences.map((m) => m.presence_ref);\n                const joinedPresences = newPresences.filter((m) => curPresenceRefs.indexOf(m.presence_ref) < 0);\n                const leftPresences = currentPresences.filter((m) => newPresenceRefs.indexOf(m.presence_ref) < 0);\n                if (joinedPresences.length > 0) {\n                    joins[key] = joinedPresences;\n                }\n                if (leftPresences.length > 0) {\n                    leaves[key] = leftPresences;\n                }\n            }\n            else {\n                joins[key] = newPresences;\n            }\n        });\n        return this.syncDiff(state, { joins, leaves }, onJoin, onLeave);\n    }\n    /**\n     * Used to sync a diff of presence join and leave events from the\n     * server, as they happen.\n     *\n     * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n     * `onLeave` callbacks to react to a user joining or leaving from a\n     * device.\n     *\n     * @internal\n     */\n    static syncDiff(state, diff, onJoin, onLeave) {\n        const { joins, leaves } = {\n            joins: this.transformState(diff.joins),\n            leaves: this.transformState(diff.leaves),\n        };\n        if (!onJoin) {\n            onJoin = () => { };\n        }\n        if (!onLeave) {\n            onLeave = () => { };\n        }\n        this.map(joins, (key, newPresences) => {\n            var _a;\n            const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n            state[key] = this.cloneDeep(newPresences);\n            if (currentPresences.length > 0) {\n                const joinedPresenceRefs = state[key].map((m) => m.presence_ref);\n                const curPresences = currentPresences.filter((m) => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n                state[key].unshift(...curPresences);\n            }\n            onJoin(key, currentPresences, newPresences);\n        });\n        this.map(leaves, (key, leftPresences) => {\n            let currentPresences = state[key];\n            if (!currentPresences)\n                return;\n            const presenceRefsToRemove = leftPresences.map((m) => m.presence_ref);\n            currentPresences = currentPresences.filter((m) => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n            state[key] = currentPresences;\n            onLeave(key, currentPresences, leftPresences);\n            if (currentPresences.length === 0)\n                delete state[key];\n        });\n        return state;\n    }\n    /** @internal */\n    static map(obj, func) {\n        return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]));\n    }\n    /**\n     * Remove 'metas' key\n     * Change 'phx_ref' to 'presence_ref'\n     * Remove 'phx_ref' and 'phx_ref_prev'\n     *\n     * @example\n     * // returns {\n     *  abc123: [\n     *    { presence_ref: '2', user_id: 1 },\n     *    { presence_ref: '3', user_id: 2 }\n     *  ]\n     * }\n     * RealtimePresence.transformState({\n     *  abc123: {\n     *    metas: [\n     *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n     *      { phx_ref: '3', user_id: 2 }\n     *    ]\n     *  }\n     * })\n     *\n     * @internal\n     */\n    static transformState(state) {\n        state = this.cloneDeep(state);\n        return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n            const presences = state[key];\n            if ('metas' in presences) {\n                newState[key] = presences.metas.map((presence) => {\n                    presence['presence_ref'] = presence['phx_ref'];\n                    delete presence['phx_ref'];\n                    delete presence['phx_ref_prev'];\n                    return presence;\n                });\n            }\n            else {\n                newState[key] = presences;\n            }\n            return newState;\n        }, {});\n    }\n    /** @internal */\n    static cloneDeep(obj) {\n        return JSON.parse(JSON.stringify(obj));\n    }\n    /** @internal */\n    onJoin(callback) {\n        this.caller.onJoin = callback;\n    }\n    /** @internal */\n    onLeave(callback) {\n        this.caller.onLeave = callback;\n    }\n    /** @internal */\n    onSync(callback) {\n        this.caller.onSync = callback;\n    }\n    /** @internal */\n    inPendingSyncState() {\n        return !this.joinRef || this.joinRef !== this.channel._joinRef();\n    }\n}\n//# sourceMappingURL=RealtimePresence.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3JlYWx0aW1lLWpzL2Rpc3QvbW9kdWxlL1JlYWx0aW1lUHJlc2VuY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDBFQUEwRTtBQUM1RDtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsU0FBUyw4QkFBOEI7QUFDbkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0IsOEJBQThCO0FBQzlCLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDLG9CQUFvQiwwQkFBMEI7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLFNBQVM7QUFDVCx3Q0FBd0M7QUFDeEMsb0JBQW9CLDBCQUEwQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQSxnREFBZ0QsZUFBZTtBQUMvRCxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULHNDQUFzQyxlQUFlO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixnQkFBZ0I7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksK0JBQStCO0FBQzNDLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw0Q0FBNEM7QUFDMUQsY0FBYztBQUNkO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsSUFBSTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStyZWFsdGltZS1qc0AyLjExLjJcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxyZWFsdGltZS1qc1xcZGlzdFxcbW9kdWxlXFxSZWFsdGltZVByZXNlbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gIFRoaXMgZmlsZSBkcmF3cyBoZWF2aWx5IGZyb20gaHR0cHM6Ly9naXRodWIuY29tL3Bob2VuaXhmcmFtZXdvcmsvcGhvZW5peC9ibG9iL2QzNDRlYzBhNzMyYWI0ZWUyMDQyMTViMzFkZTY5Y2Y0YmU3MmUzYmYvYXNzZXRzL2pzL3Bob2VuaXgvcHJlc2VuY2UuanNcbiAgTGljZW5zZTogaHR0cHM6Ly9naXRodWIuY29tL3Bob2VuaXhmcmFtZXdvcmsvcGhvZW5peC9ibG9iL2QzNDRlYzBhNzMyYWI0ZWUyMDQyMTViMzFkZTY5Y2Y0YmU3MmUzYmYvTElDRU5TRS5tZFxuKi9cbmV4cG9ydCB2YXIgUkVBTFRJTUVfUFJFU0VOQ0VfTElTVEVOX0VWRU5UUztcbihmdW5jdGlvbiAoUkVBTFRJTUVfUFJFU0VOQ0VfTElTVEVOX0VWRU5UUykge1xuICAgIFJFQUxUSU1FX1BSRVNFTkNFX0xJU1RFTl9FVkVOVFNbXCJTWU5DXCJdID0gXCJzeW5jXCI7XG4gICAgUkVBTFRJTUVfUFJFU0VOQ0VfTElTVEVOX0VWRU5UU1tcIkpPSU5cIl0gPSBcImpvaW5cIjtcbiAgICBSRUFMVElNRV9QUkVTRU5DRV9MSVNURU5fRVZFTlRTW1wiTEVBVkVcIl0gPSBcImxlYXZlXCI7XG59KShSRUFMVElNRV9QUkVTRU5DRV9MSVNURU5fRVZFTlRTIHx8IChSRUFMVElNRV9QUkVTRU5DRV9MSVNURU5fRVZFTlRTID0ge30pKTtcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFJlYWx0aW1lUHJlc2VuY2Uge1xuICAgIC8qKlxuICAgICAqIEluaXRpYWxpemVzIHRoZSBQcmVzZW5jZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBjaGFubmVsIC0gVGhlIFJlYWx0aW1lQ2hhbm5lbFxuICAgICAqIEBwYXJhbSBvcHRzIC0gVGhlIG9wdGlvbnMsXG4gICAgICogICAgICAgIGZvciBleGFtcGxlIGB7ZXZlbnRzOiB7c3RhdGU6ICdzdGF0ZScsIGRpZmY6ICdkaWZmJ319YFxuICAgICAqL1xuICAgIGNvbnN0cnVjdG9yKGNoYW5uZWwsIG9wdHMpIHtcbiAgICAgICAgdGhpcy5jaGFubmVsID0gY2hhbm5lbDtcbiAgICAgICAgdGhpcy5zdGF0ZSA9IHt9O1xuICAgICAgICB0aGlzLnBlbmRpbmdEaWZmcyA9IFtdO1xuICAgICAgICB0aGlzLmpvaW5SZWYgPSBudWxsO1xuICAgICAgICB0aGlzLmNhbGxlciA9IHtcbiAgICAgICAgICAgIG9uSm9pbjogKCkgPT4geyB9LFxuICAgICAgICAgICAgb25MZWF2ZTogKCkgPT4geyB9LFxuICAgICAgICAgICAgb25TeW5jOiAoKSA9PiB7IH0sXG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGV2ZW50cyA9IChvcHRzID09PSBudWxsIHx8IG9wdHMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdHMuZXZlbnRzKSB8fCB7XG4gICAgICAgICAgICBzdGF0ZTogJ3ByZXNlbmNlX3N0YXRlJyxcbiAgICAgICAgICAgIGRpZmY6ICdwcmVzZW5jZV9kaWZmJyxcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5jaGFubmVsLl9vbihldmVudHMuc3RhdGUsIHt9LCAobmV3U3RhdGUpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHsgb25Kb2luLCBvbkxlYXZlLCBvblN5bmMgfSA9IHRoaXMuY2FsbGVyO1xuICAgICAgICAgICAgdGhpcy5qb2luUmVmID0gdGhpcy5jaGFubmVsLl9qb2luUmVmKCk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gUmVhbHRpbWVQcmVzZW5jZS5zeW5jU3RhdGUodGhpcy5zdGF0ZSwgbmV3U3RhdGUsIG9uSm9pbiwgb25MZWF2ZSk7XG4gICAgICAgICAgICB0aGlzLnBlbmRpbmdEaWZmcy5mb3JFYWNoKChkaWZmKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFJlYWx0aW1lUHJlc2VuY2Uuc3luY0RpZmYodGhpcy5zdGF0ZSwgZGlmZiwgb25Kb2luLCBvbkxlYXZlKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdGhpcy5wZW5kaW5nRGlmZnMgPSBbXTtcbiAgICAgICAgICAgIG9uU3luYygpO1xuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5jaGFubmVsLl9vbihldmVudHMuZGlmZiwge30sIChkaWZmKSA9PiB7XG4gICAgICAgICAgICBjb25zdCB7IG9uSm9pbiwgb25MZWF2ZSwgb25TeW5jIH0gPSB0aGlzLmNhbGxlcjtcbiAgICAgICAgICAgIGlmICh0aGlzLmluUGVuZGluZ1N5bmNTdGF0ZSgpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5wZW5kaW5nRGlmZnMucHVzaChkaWZmKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBSZWFsdGltZVByZXNlbmNlLnN5bmNEaWZmKHRoaXMuc3RhdGUsIGRpZmYsIG9uSm9pbiwgb25MZWF2ZSk7XG4gICAgICAgICAgICAgICAgb25TeW5jKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLm9uSm9pbigoa2V5LCBjdXJyZW50UHJlc2VuY2VzLCBuZXdQcmVzZW5jZXMpID0+IHtcbiAgICAgICAgICAgIHRoaXMuY2hhbm5lbC5fdHJpZ2dlcigncHJlc2VuY2UnLCB7XG4gICAgICAgICAgICAgICAgZXZlbnQ6ICdqb2luJyxcbiAgICAgICAgICAgICAgICBrZXksXG4gICAgICAgICAgICAgICAgY3VycmVudFByZXNlbmNlcyxcbiAgICAgICAgICAgICAgICBuZXdQcmVzZW5jZXMsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMub25MZWF2ZSgoa2V5LCBjdXJyZW50UHJlc2VuY2VzLCBsZWZ0UHJlc2VuY2VzKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmNoYW5uZWwuX3RyaWdnZXIoJ3ByZXNlbmNlJywge1xuICAgICAgICAgICAgICAgIGV2ZW50OiAnbGVhdmUnLFxuICAgICAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgICAgICBjdXJyZW50UHJlc2VuY2VzLFxuICAgICAgICAgICAgICAgIGxlZnRQcmVzZW5jZXMsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMub25TeW5jKCgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuY2hhbm5lbC5fdHJpZ2dlcigncHJlc2VuY2UnLCB7IGV2ZW50OiAnc3luYycgfSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2VkIHRvIHN5bmMgdGhlIGxpc3Qgb2YgcHJlc2VuY2VzIG9uIHRoZSBzZXJ2ZXIgd2l0aCB0aGVcbiAgICAgKiBjbGllbnQncyBzdGF0ZS5cbiAgICAgKlxuICAgICAqIEFuIG9wdGlvbmFsIGBvbkpvaW5gIGFuZCBgb25MZWF2ZWAgY2FsbGJhY2sgY2FuIGJlIHByb3ZpZGVkIHRvXG4gICAgICogcmVhY3QgdG8gY2hhbmdlcyBpbiB0aGUgY2xpZW50J3MgbG9jYWwgcHJlc2VuY2VzIGFjcm9zc1xuICAgICAqIGRpc2Nvbm5lY3RzIGFuZCByZWNvbm5lY3RzIHdpdGggdGhlIHNlcnZlci5cbiAgICAgKlxuICAgICAqIEBpbnRlcm5hbFxuICAgICAqL1xuICAgIHN0YXRpYyBzeW5jU3RhdGUoY3VycmVudFN0YXRlLCBuZXdTdGF0ZSwgb25Kb2luLCBvbkxlYXZlKSB7XG4gICAgICAgIGNvbnN0IHN0YXRlID0gdGhpcy5jbG9uZURlZXAoY3VycmVudFN0YXRlKTtcbiAgICAgICAgY29uc3QgdHJhbnNmb3JtZWRTdGF0ZSA9IHRoaXMudHJhbnNmb3JtU3RhdGUobmV3U3RhdGUpO1xuICAgICAgICBjb25zdCBqb2lucyA9IHt9O1xuICAgICAgICBjb25zdCBsZWF2ZXMgPSB7fTtcbiAgICAgICAgdGhpcy5tYXAoc3RhdGUsIChrZXksIHByZXNlbmNlcykgPT4ge1xuICAgICAgICAgICAgaWYgKCF0cmFuc2Zvcm1lZFN0YXRlW2tleV0pIHtcbiAgICAgICAgICAgICAgICBsZWF2ZXNba2V5XSA9IHByZXNlbmNlcztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMubWFwKHRyYW5zZm9ybWVkU3RhdGUsIChrZXksIG5ld1ByZXNlbmNlcykgPT4ge1xuICAgICAgICAgICAgY29uc3QgY3VycmVudFByZXNlbmNlcyA9IHN0YXRlW2tleV07XG4gICAgICAgICAgICBpZiAoY3VycmVudFByZXNlbmNlcykge1xuICAgICAgICAgICAgICAgIGNvbnN0IG5ld1ByZXNlbmNlUmVmcyA9IG5ld1ByZXNlbmNlcy5tYXAoKG0pID0+IG0ucHJlc2VuY2VfcmVmKTtcbiAgICAgICAgICAgICAgICBjb25zdCBjdXJQcmVzZW5jZVJlZnMgPSBjdXJyZW50UHJlc2VuY2VzLm1hcCgobSkgPT4gbS5wcmVzZW5jZV9yZWYpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGpvaW5lZFByZXNlbmNlcyA9IG5ld1ByZXNlbmNlcy5maWx0ZXIoKG0pID0+IGN1clByZXNlbmNlUmVmcy5pbmRleE9mKG0ucHJlc2VuY2VfcmVmKSA8IDApO1xuICAgICAgICAgICAgICAgIGNvbnN0IGxlZnRQcmVzZW5jZXMgPSBjdXJyZW50UHJlc2VuY2VzLmZpbHRlcigobSkgPT4gbmV3UHJlc2VuY2VSZWZzLmluZGV4T2YobS5wcmVzZW5jZV9yZWYpIDwgMCk7XG4gICAgICAgICAgICAgICAgaWYgKGpvaW5lZFByZXNlbmNlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgIGpvaW5zW2tleV0gPSBqb2luZWRQcmVzZW5jZXM7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChsZWZ0UHJlc2VuY2VzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgbGVhdmVzW2tleV0gPSBsZWZ0UHJlc2VuY2VzO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGpvaW5zW2tleV0gPSBuZXdQcmVzZW5jZXM7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gdGhpcy5zeW5jRGlmZihzdGF0ZSwgeyBqb2lucywgbGVhdmVzIH0sIG9uSm9pbiwgb25MZWF2ZSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZWQgdG8gc3luYyBhIGRpZmYgb2YgcHJlc2VuY2Ugam9pbiBhbmQgbGVhdmUgZXZlbnRzIGZyb20gdGhlXG4gICAgICogc2VydmVyLCBhcyB0aGV5IGhhcHBlbi5cbiAgICAgKlxuICAgICAqIExpa2UgYHN5bmNTdGF0ZWAsIGBzeW5jRGlmZmAgYWNjZXB0cyBvcHRpb25hbCBgb25Kb2luYCBhbmRcbiAgICAgKiBgb25MZWF2ZWAgY2FsbGJhY2tzIHRvIHJlYWN0IHRvIGEgdXNlciBqb2luaW5nIG9yIGxlYXZpbmcgZnJvbSBhXG4gICAgICogZGV2aWNlLlxuICAgICAqXG4gICAgICogQGludGVybmFsXG4gICAgICovXG4gICAgc3RhdGljIHN5bmNEaWZmKHN0YXRlLCBkaWZmLCBvbkpvaW4sIG9uTGVhdmUpIHtcbiAgICAgICAgY29uc3QgeyBqb2lucywgbGVhdmVzIH0gPSB7XG4gICAgICAgICAgICBqb2luczogdGhpcy50cmFuc2Zvcm1TdGF0ZShkaWZmLmpvaW5zKSxcbiAgICAgICAgICAgIGxlYXZlczogdGhpcy50cmFuc2Zvcm1TdGF0ZShkaWZmLmxlYXZlcyksXG4gICAgICAgIH07XG4gICAgICAgIGlmICghb25Kb2luKSB7XG4gICAgICAgICAgICBvbkpvaW4gPSAoKSA9PiB7IH07XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFvbkxlYXZlKSB7XG4gICAgICAgICAgICBvbkxlYXZlID0gKCkgPT4geyB9O1xuICAgICAgICB9XG4gICAgICAgIHRoaXMubWFwKGpvaW5zLCAoa2V5LCBuZXdQcmVzZW5jZXMpID0+IHtcbiAgICAgICAgICAgIHZhciBfYTtcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRQcmVzZW5jZXMgPSAoX2EgPSBzdGF0ZVtrZXldKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBbXTtcbiAgICAgICAgICAgIHN0YXRlW2tleV0gPSB0aGlzLmNsb25lRGVlcChuZXdQcmVzZW5jZXMpO1xuICAgICAgICAgICAgaWYgKGN1cnJlbnRQcmVzZW5jZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGpvaW5lZFByZXNlbmNlUmVmcyA9IHN0YXRlW2tleV0ubWFwKChtKSA9PiBtLnByZXNlbmNlX3JlZik7XG4gICAgICAgICAgICAgICAgY29uc3QgY3VyUHJlc2VuY2VzID0gY3VycmVudFByZXNlbmNlcy5maWx0ZXIoKG0pID0+IGpvaW5lZFByZXNlbmNlUmVmcy5pbmRleE9mKG0ucHJlc2VuY2VfcmVmKSA8IDApO1xuICAgICAgICAgICAgICAgIHN0YXRlW2tleV0udW5zaGlmdCguLi5jdXJQcmVzZW5jZXMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgb25Kb2luKGtleSwgY3VycmVudFByZXNlbmNlcywgbmV3UHJlc2VuY2VzKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMubWFwKGxlYXZlcywgKGtleSwgbGVmdFByZXNlbmNlcykgPT4ge1xuICAgICAgICAgICAgbGV0IGN1cnJlbnRQcmVzZW5jZXMgPSBzdGF0ZVtrZXldO1xuICAgICAgICAgICAgaWYgKCFjdXJyZW50UHJlc2VuY2VzKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIGNvbnN0IHByZXNlbmNlUmVmc1RvUmVtb3ZlID0gbGVmdFByZXNlbmNlcy5tYXAoKG0pID0+IG0ucHJlc2VuY2VfcmVmKTtcbiAgICAgICAgICAgIGN1cnJlbnRQcmVzZW5jZXMgPSBjdXJyZW50UHJlc2VuY2VzLmZpbHRlcigobSkgPT4gcHJlc2VuY2VSZWZzVG9SZW1vdmUuaW5kZXhPZihtLnByZXNlbmNlX3JlZikgPCAwKTtcbiAgICAgICAgICAgIHN0YXRlW2tleV0gPSBjdXJyZW50UHJlc2VuY2VzO1xuICAgICAgICAgICAgb25MZWF2ZShrZXksIGN1cnJlbnRQcmVzZW5jZXMsIGxlZnRQcmVzZW5jZXMpO1xuICAgICAgICAgICAgaWYgKGN1cnJlbnRQcmVzZW5jZXMubGVuZ3RoID09PSAwKVxuICAgICAgICAgICAgICAgIGRlbGV0ZSBzdGF0ZVtrZXldO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHN0YXRlO1xuICAgIH1cbiAgICAvKiogQGludGVybmFsICovXG4gICAgc3RhdGljIG1hcChvYmosIGZ1bmMpIHtcbiAgICAgICAgcmV0dXJuIE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzKG9iaikubWFwKChrZXkpID0+IGZ1bmMoa2V5LCBvYmpba2V5XSkpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZW1vdmUgJ21ldGFzJyBrZXlcbiAgICAgKiBDaGFuZ2UgJ3BoeF9yZWYnIHRvICdwcmVzZW5jZV9yZWYnXG4gICAgICogUmVtb3ZlICdwaHhfcmVmJyBhbmQgJ3BoeF9yZWZfcHJldidcbiAgICAgKlxuICAgICAqIEBleGFtcGxlXG4gICAgICogLy8gcmV0dXJucyB7XG4gICAgICogIGFiYzEyMzogW1xuICAgICAqICAgIHsgcHJlc2VuY2VfcmVmOiAnMicsIHVzZXJfaWQ6IDEgfSxcbiAgICAgKiAgICB7IHByZXNlbmNlX3JlZjogJzMnLCB1c2VyX2lkOiAyIH1cbiAgICAgKiAgXVxuICAgICAqIH1cbiAgICAgKiBSZWFsdGltZVByZXNlbmNlLnRyYW5zZm9ybVN0YXRlKHtcbiAgICAgKiAgYWJjMTIzOiB7XG4gICAgICogICAgbWV0YXM6IFtcbiAgICAgKiAgICAgIHsgcGh4X3JlZjogJzInLCBwaHhfcmVmX3ByZXY6ICcxJyB1c2VyX2lkOiAxIH0sXG4gICAgICogICAgICB7IHBoeF9yZWY6ICczJywgdXNlcl9pZDogMiB9XG4gICAgICogICAgXVxuICAgICAqICB9XG4gICAgICogfSlcbiAgICAgKlxuICAgICAqIEBpbnRlcm5hbFxuICAgICAqL1xuICAgIHN0YXRpYyB0cmFuc2Zvcm1TdGF0ZShzdGF0ZSkge1xuICAgICAgICBzdGF0ZSA9IHRoaXMuY2xvbmVEZWVwKHN0YXRlKTtcbiAgICAgICAgcmV0dXJuIE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzKHN0YXRlKS5yZWR1Y2UoKG5ld1N0YXRlLCBrZXkpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHByZXNlbmNlcyA9IHN0YXRlW2tleV07XG4gICAgICAgICAgICBpZiAoJ21ldGFzJyBpbiBwcmVzZW5jZXMpIHtcbiAgICAgICAgICAgICAgICBuZXdTdGF0ZVtrZXldID0gcHJlc2VuY2VzLm1ldGFzLm1hcCgocHJlc2VuY2UpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgcHJlc2VuY2VbJ3ByZXNlbmNlX3JlZiddID0gcHJlc2VuY2VbJ3BoeF9yZWYnXTtcbiAgICAgICAgICAgICAgICAgICAgZGVsZXRlIHByZXNlbmNlWydwaHhfcmVmJ107XG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBwcmVzZW5jZVsncGh4X3JlZl9wcmV2J107XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBwcmVzZW5jZTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIG5ld1N0YXRlW2tleV0gPSBwcmVzZW5jZXM7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gbmV3U3RhdGU7XG4gICAgICAgIH0sIHt9KTtcbiAgICB9XG4gICAgLyoqIEBpbnRlcm5hbCAqL1xuICAgIHN0YXRpYyBjbG9uZURlZXAob2JqKSB7XG4gICAgICAgIHJldHVybiBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KG9iaikpO1xuICAgIH1cbiAgICAvKiogQGludGVybmFsICovXG4gICAgb25Kb2luKGNhbGxiYWNrKSB7XG4gICAgICAgIHRoaXMuY2FsbGVyLm9uSm9pbiA9IGNhbGxiYWNrO1xuICAgIH1cbiAgICAvKiogQGludGVybmFsICovXG4gICAgb25MZWF2ZShjYWxsYmFjaykge1xuICAgICAgICB0aGlzLmNhbGxlci5vbkxlYXZlID0gY2FsbGJhY2s7XG4gICAgfVxuICAgIC8qKiBAaW50ZXJuYWwgKi9cbiAgICBvblN5bmMoY2FsbGJhY2spIHtcbiAgICAgICAgdGhpcy5jYWxsZXIub25TeW5jID0gY2FsbGJhY2s7XG4gICAgfVxuICAgIC8qKiBAaW50ZXJuYWwgKi9cbiAgICBpblBlbmRpbmdTeW5jU3RhdGUoKSB7XG4gICAgICAgIHJldHVybiAhdGhpcy5qb2luUmVmIHx8IHRoaXMuam9pblJlZiAhPT0gdGhpcy5jaGFubmVsLl9qb2luUmVmKCk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9UmVhbHRpbWVQcmVzZW5jZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RealtimeClient */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RealtimePresence */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3JlYWx0aW1lLWpzL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQThDO0FBQzBIO0FBQ2hGO0FBQzBIO0FBQ2xOIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHphY2hhXFxPbmVEcml2ZVxcRGVza3RvcFxcVW5pVmliZVxcVW5pVmliZS1wcm9qZWN0LXRyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcmVhbHRpbWUtanNAMi4xMS4yXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccmVhbHRpbWUtanNcXGRpc3RcXG1vZHVsZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWx0aW1lQ2xpZW50IGZyb20gJy4vUmVhbHRpbWVDbGllbnQnO1xuaW1wb3J0IFJlYWx0aW1lQ2hhbm5lbCwgeyBSRUFMVElNRV9MSVNURU5fVFlQRVMsIFJFQUxUSU1FX1BPU1RHUkVTX0NIQU5HRVNfTElTVEVOX0VWRU5ULCBSRUFMVElNRV9TVUJTQ1JJQkVfU1RBVEVTLCBSRUFMVElNRV9DSEFOTkVMX1NUQVRFUywgfSBmcm9tICcuL1JlYWx0aW1lQ2hhbm5lbCc7XG5pbXBvcnQgUmVhbHRpbWVQcmVzZW5jZSwgeyBSRUFMVElNRV9QUkVTRU5DRV9MSVNURU5fRVZFTlRTLCB9IGZyb20gJy4vUmVhbHRpbWVQcmVzZW5jZSc7XG5leHBvcnQgeyBSZWFsdGltZVByZXNlbmNlLCBSZWFsdGltZUNoYW5uZWwsIFJlYWx0aW1lQ2xpZW50LCBSRUFMVElNRV9MSVNURU5fVFlQRVMsIFJFQUxUSU1FX1BPU1RHUkVTX0NIQU5HRVNfTElTVEVOX0VWRU5ULCBSRUFMVElNRV9QUkVTRU5DRV9MSVNURU5fRVZFTlRTLCBSRUFMVElNRV9TVUJTQ1JJQkVfU1RBVEVTLCBSRUFMVElNRV9DSEFOTkVMX1NUQVRFUywgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CHANNEL_EVENTS: () => (/* binding */ CHANNEL_EVENTS),\n/* harmony export */   CHANNEL_STATES: () => (/* binding */ CHANNEL_STATES),\n/* harmony export */   CONNECTION_STATE: () => (/* binding */ CONNECTION_STATE),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_TIMEOUT: () => (/* binding */ DEFAULT_TIMEOUT),\n/* harmony export */   SOCKET_STATES: () => (/* binding */ SOCKET_STATES),\n/* harmony export */   TRANSPORTS: () => (/* binding */ TRANSPORTS),\n/* harmony export */   VSN: () => (/* binding */ VSN),\n/* harmony export */   WS_CLOSE_NORMAL: () => (/* binding */ WS_CLOSE_NORMAL)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/version.js\");\n\nconst DEFAULT_HEADERS = { 'X-Client-Info': `realtime-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst VSN = '1.0.0';\nconst DEFAULT_TIMEOUT = 10000;\nconst WS_CLOSE_NORMAL = 1000;\nvar SOCKET_STATES;\n(function (SOCKET_STATES) {\n    SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n    SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n    SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n    SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nvar CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n    CHANNEL_STATES[\"closed\"] = \"closed\";\n    CHANNEL_STATES[\"errored\"] = \"errored\";\n    CHANNEL_STATES[\"joined\"] = \"joined\";\n    CHANNEL_STATES[\"joining\"] = \"joining\";\n    CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nvar CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n    CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n    CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n    CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n    CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n    CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n    CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nvar TRANSPORTS;\n(function (TRANSPORTS) {\n    TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nvar CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n    CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n    CONNECTION_STATE[\"Open\"] = \"open\";\n    CONNECTION_STATE[\"Closing\"] = \"closing\";\n    CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {}));\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/push.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/push.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Push)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n\nclass Push {\n    /**\n     * Initializes the Push\n     *\n     * @param channel The Channel\n     * @param event The event, for example `\"phx_join\"`\n     * @param payload The payload, for example `{user_id: 123}`\n     * @param timeout The push timeout in milliseconds\n     */\n    constructor(channel, event, payload = {}, timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TIMEOUT) {\n        this.channel = channel;\n        this.event = event;\n        this.payload = payload;\n        this.timeout = timeout;\n        this.sent = false;\n        this.timeoutTimer = undefined;\n        this.ref = '';\n        this.receivedResp = null;\n        this.recHooks = [];\n        this.refEvent = null;\n    }\n    resend(timeout) {\n        this.timeout = timeout;\n        this._cancelRefEvent();\n        this.ref = '';\n        this.refEvent = null;\n        this.receivedResp = null;\n        this.sent = false;\n        this.send();\n    }\n    send() {\n        if (this._hasReceived('timeout')) {\n            return;\n        }\n        this.startTimeout();\n        this.sent = true;\n        this.channel.socket.push({\n            topic: this.channel.topic,\n            event: this.event,\n            payload: this.payload,\n            ref: this.ref,\n            join_ref: this.channel._joinRef(),\n        });\n    }\n    updatePayload(payload) {\n        this.payload = Object.assign(Object.assign({}, this.payload), payload);\n    }\n    receive(status, callback) {\n        var _a;\n        if (this._hasReceived(status)) {\n            callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n        }\n        this.recHooks.push({ status, callback });\n        return this;\n    }\n    startTimeout() {\n        if (this.timeoutTimer) {\n            return;\n        }\n        this.ref = this.channel.socket._makeRef();\n        this.refEvent = this.channel._replyEventName(this.ref);\n        const callback = (payload) => {\n            this._cancelRefEvent();\n            this._cancelTimeout();\n            this.receivedResp = payload;\n            this._matchReceive(payload);\n        };\n        this.channel._on(this.refEvent, {}, callback);\n        this.timeoutTimer = setTimeout(() => {\n            this.trigger('timeout', {});\n        }, this.timeout);\n    }\n    trigger(status, response) {\n        if (this.refEvent)\n            this.channel._trigger(this.refEvent, { status, response });\n    }\n    destroy() {\n        this._cancelRefEvent();\n        this._cancelTimeout();\n    }\n    _cancelRefEvent() {\n        if (!this.refEvent) {\n            return;\n        }\n        this.channel._off(this.refEvent, {});\n    }\n    _cancelTimeout() {\n        clearTimeout(this.timeoutTimer);\n        this.timeoutTimer = undefined;\n    }\n    _matchReceive({ status, response, }) {\n        this.recHooks\n            .filter((h) => h.status === status)\n            .forEach((h) => h.callback(response));\n    }\n    _hasReceived(status) {\n        return this.receivedResp && this.receivedResp.status === status;\n    }\n}\n//# sourceMappingURL=push.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/push.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Serializer)\n/* harmony export */ });\n// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\nclass Serializer {\n    constructor() {\n        this.HEADER_LENGTH = 1;\n    }\n    decode(rawPayload, callback) {\n        if (rawPayload.constructor === ArrayBuffer) {\n            return callback(this._binaryDecode(rawPayload));\n        }\n        if (typeof rawPayload === 'string') {\n            return callback(JSON.parse(rawPayload));\n        }\n        return callback({});\n    }\n    _binaryDecode(buffer) {\n        const view = new DataView(buffer);\n        const decoder = new TextDecoder();\n        return this._decodeBroadcast(buffer, view, decoder);\n    }\n    _decodeBroadcast(buffer, view, decoder) {\n        const topicSize = view.getUint8(1);\n        const eventSize = view.getUint8(2);\n        let offset = this.HEADER_LENGTH + 2;\n        const topic = decoder.decode(buffer.slice(offset, offset + topicSize));\n        offset = offset + topicSize;\n        const event = decoder.decode(buffer.slice(offset, offset + eventSize));\n        offset = offset + eventSize;\n        const data = JSON.parse(decoder.decode(buffer.slice(offset, buffer.byteLength)));\n        return { ref: null, topic: topic, event: event, payload: data };\n    }\n}\n//# sourceMappingURL=serializer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Timer)\n/* harmony export */ });\n/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nclass Timer {\n    constructor(callback, timerCalc) {\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n        this.timer = undefined;\n        this.tries = 0;\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n    }\n    reset() {\n        this.tries = 0;\n        clearTimeout(this.timer);\n    }\n    // Cancels any previous scheduleTimeout and schedules callback\n    scheduleTimeout() {\n        clearTimeout(this.timer);\n        this.timer = setTimeout(() => {\n            this.tries = this.tries + 1;\n            this.callback();\n        }, this.timerCalc(this.tries + 1));\n    }\n}\n//# sourceMappingURL=timer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgresTypes: () => (/* binding */ PostgresTypes),\n/* harmony export */   convertCell: () => (/* binding */ convertCell),\n/* harmony export */   convertChangeData: () => (/* binding */ convertChangeData),\n/* harmony export */   convertColumn: () => (/* binding */ convertColumn),\n/* harmony export */   httpEndpointURL: () => (/* binding */ httpEndpointURL),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toJson: () => (/* binding */ toJson),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toTimestampString: () => (/* binding */ toTimestampString)\n/* harmony export */ });\n/**\n * Helpers to convert the change Payload into native JS types.\n */\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\nvar PostgresTypes;\n(function (PostgresTypes) {\n    PostgresTypes[\"abstime\"] = \"abstime\";\n    PostgresTypes[\"bool\"] = \"bool\";\n    PostgresTypes[\"date\"] = \"date\";\n    PostgresTypes[\"daterange\"] = \"daterange\";\n    PostgresTypes[\"float4\"] = \"float4\";\n    PostgresTypes[\"float8\"] = \"float8\";\n    PostgresTypes[\"int2\"] = \"int2\";\n    PostgresTypes[\"int4\"] = \"int4\";\n    PostgresTypes[\"int4range\"] = \"int4range\";\n    PostgresTypes[\"int8\"] = \"int8\";\n    PostgresTypes[\"int8range\"] = \"int8range\";\n    PostgresTypes[\"json\"] = \"json\";\n    PostgresTypes[\"jsonb\"] = \"jsonb\";\n    PostgresTypes[\"money\"] = \"money\";\n    PostgresTypes[\"numeric\"] = \"numeric\";\n    PostgresTypes[\"oid\"] = \"oid\";\n    PostgresTypes[\"reltime\"] = \"reltime\";\n    PostgresTypes[\"text\"] = \"text\";\n    PostgresTypes[\"time\"] = \"time\";\n    PostgresTypes[\"timestamp\"] = \"timestamp\";\n    PostgresTypes[\"timestamptz\"] = \"timestamptz\";\n    PostgresTypes[\"timetz\"] = \"timetz\";\n    PostgresTypes[\"tsrange\"] = \"tsrange\";\n    PostgresTypes[\"tstzrange\"] = \"tstzrange\";\n})(PostgresTypes || (PostgresTypes = {}));\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nconst convertChangeData = (columns, record, options = {}) => {\n    var _a;\n    const skipTypes = (_a = options.skipTypes) !== null && _a !== void 0 ? _a : [];\n    return Object.keys(record).reduce((acc, rec_key) => {\n        acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes);\n        return acc;\n    }, {});\n};\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nconst convertColumn = (columnName, columns, record, skipTypes) => {\n    const column = columns.find((x) => x.name === columnName);\n    const colType = column === null || column === void 0 ? void 0 : column.type;\n    const value = record[columnName];\n    if (colType && !skipTypes.includes(colType)) {\n        return convertCell(colType, value);\n    }\n    return noop(value);\n};\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nconst convertCell = (type, value) => {\n    // if data type is an array\n    if (type.charAt(0) === '_') {\n        const dataType = type.slice(1, type.length);\n        return toArray(value, dataType);\n    }\n    // If not null, convert to correct type.\n    switch (type) {\n        case PostgresTypes.bool:\n            return toBoolean(value);\n        case PostgresTypes.float4:\n        case PostgresTypes.float8:\n        case PostgresTypes.int2:\n        case PostgresTypes.int4:\n        case PostgresTypes.int8:\n        case PostgresTypes.numeric:\n        case PostgresTypes.oid:\n            return toNumber(value);\n        case PostgresTypes.json:\n        case PostgresTypes.jsonb:\n            return toJson(value);\n        case PostgresTypes.timestamp:\n            return toTimestampString(value); // Format to be consistent with PostgREST\n        case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n        case PostgresTypes.date: // To allow users to cast it based on Timezone\n        case PostgresTypes.daterange:\n        case PostgresTypes.int4range:\n        case PostgresTypes.int8range:\n        case PostgresTypes.money:\n        case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n        case PostgresTypes.text:\n        case PostgresTypes.time: // To allow users to cast it based on Timezone\n        case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n        case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n        case PostgresTypes.tsrange:\n        case PostgresTypes.tstzrange:\n            return noop(value);\n        default:\n            // Return the value for remaining types\n            return noop(value);\n    }\n};\nconst noop = (value) => {\n    return value;\n};\nconst toBoolean = (value) => {\n    switch (value) {\n        case 't':\n            return true;\n        case 'f':\n            return false;\n        default:\n            return value;\n    }\n};\nconst toNumber = (value) => {\n    if (typeof value === 'string') {\n        const parsedValue = parseFloat(value);\n        if (!Number.isNaN(parsedValue)) {\n            return parsedValue;\n        }\n    }\n    return value;\n};\nconst toJson = (value) => {\n    if (typeof value === 'string') {\n        try {\n            return JSON.parse(value);\n        }\n        catch (error) {\n            console.log(`JSON parse error: ${error}`);\n            return value;\n        }\n    }\n    return value;\n};\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nconst toArray = (value, type) => {\n    if (typeof value !== 'string') {\n        return value;\n    }\n    const lastIdx = value.length - 1;\n    const closeBrace = value[lastIdx];\n    const openBrace = value[0];\n    // Confirm value is a Postgres array by checking curly brackets\n    if (openBrace === '{' && closeBrace === '}') {\n        let arr;\n        const valTrim = value.slice(1, lastIdx);\n        // TODO: find a better solution to separate Postgres array data\n        try {\n            arr = JSON.parse('[' + valTrim + ']');\n        }\n        catch (_) {\n            // WARNING: splitting on comma does not cover all edge cases\n            arr = valTrim ? valTrim.split(',') : [];\n        }\n        return arr.map((val) => convertCell(type, val));\n    }\n    return value;\n};\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nconst toTimestampString = (value) => {\n    if (typeof value === 'string') {\n        return value.replace(' ', 'T');\n    }\n    return value;\n};\nconst httpEndpointURL = (socketUrl) => {\n    let url = socketUrl;\n    url = url.replace(/^ws/i, 'http');\n    url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '');\n    return url.replace(/\\/+$/, '');\n};\n//# sourceMappingURL=transformers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.11.2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3JlYWx0aW1lLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHphY2hhXFxPbmVEcml2ZVxcRGVza3RvcFxcVW5pVmliZVxcVW5pVmliZS1wcm9qZWN0LXRyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcmVhbHRpbWUtanNAMi4xMS4yXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccmVhbHRpbWUtanNcXGRpc3RcXG1vZHVsZVxcbGliXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB2ZXJzaW9uID0gJzIuMTEuMic7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/version.js\n");

/***/ })

};
;