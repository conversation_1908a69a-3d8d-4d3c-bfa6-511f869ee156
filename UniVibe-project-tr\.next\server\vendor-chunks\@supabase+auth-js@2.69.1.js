"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+auth-js@2.69.1";
exports.ids = ["vendor-chunks/@supabase+auth-js@2.69.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n\nconst AuthAdminApi = _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthAdminApi);\n//# sourceMappingURL=AuthAdminApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi42OS4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9BdXRoQWRtaW5BcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFDOUMscUJBQXFCLHVEQUFjO0FBQ25DLGlFQUFlLFlBQVksRUFBQztBQUM1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx6YWNoYVxcT25lRHJpdmVcXERlc2t0b3BcXFVuaVZpYmVcXFVuaVZpYmUtcHJvamVjdC10clxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi42OS4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxBdXRoQWRtaW5BcGkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEdvVHJ1ZUFkbWluQXBpIGZyb20gJy4vR29UcnVlQWRtaW5BcGknO1xuY29uc3QgQXV0aEFkbWluQXBpID0gR29UcnVlQWRtaW5BcGk7XG5leHBvcnQgZGVmYXVsdCBBdXRoQWRtaW5BcGk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1BdXRoQWRtaW5BcGkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/AuthClient.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/AuthClient.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _GoTrueClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueClient */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\");\n\nconst AuthClient = _GoTrueClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthClient);\n//# sourceMappingURL=AuthClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi42OS4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9BdXRoQ2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDO0FBQzFDLG1CQUFtQixxREFBWTtBQUMvQixpRUFBZSxVQUFVLEVBQUM7QUFDMUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZSthdXRoLWpzQDIuNjkuMVxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGF1dGgtanNcXGRpc3RcXG1vZHVsZVxcQXV0aENsaWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgR29UcnVlQ2xpZW50IGZyb20gJy4vR29UcnVlQ2xpZW50JztcbmNvbnN0IEF1dGhDbGllbnQgPSBHb1RydWVDbGllbnQ7XG5leHBvcnQgZGVmYXVsdCBBdXRoQ2xpZW50O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QXV0aENsaWVudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/AuthClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoTrueAdminApi)\n/* harmony export */ });\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\nclass GoTrueAdminApi {\n    constructor({ url = '', headers = {}, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(fetch);\n        this.mfa = {\n            listFactors: this._listFactors.bind(this),\n            deleteFactor: this._deleteFactor.bind(this),\n        };\n    }\n    /**\n     * Removes a logged-in session.\n     * @param jwt A valid, logged-in JWT.\n     * @param scope The logout sope.\n     */\n    async signOut(jwt, scope = 'global') {\n        try {\n            await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n                headers: this.headers,\n                jwt,\n                noResolveJson: true,\n            });\n            return { data: null, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends an invite link to an email address.\n     * @param email The email address of the user.\n     * @param options Additional options to be included when inviting.\n     */\n    async inviteUserByEmail(email, options = {}) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/invite`, {\n                body: { email, data: options.data },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates email links and OTPs to be sent via a custom email provider.\n     * @param email The user's email.\n     * @param options.password User password. For signup only.\n     * @param options.data Optional user metadata. For signup only.\n     * @param options.redirectTo The redirect url which should be appended to the generated link\n     */\n    async generateLink(params) {\n        try {\n            const { options } = params, rest = __rest(params, [\"options\"]);\n            const body = Object.assign(Object.assign({}, rest), options);\n            if ('newEmail' in rest) {\n                // replace newEmail with new_email in request body\n                body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n                delete body['newEmail'];\n            }\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n                body: body,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._generateLinkResponse,\n                redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return {\n                    data: {\n                        properties: null,\n                        user: null,\n                    },\n                    error,\n                };\n            }\n            throw error;\n        }\n    }\n    // User Admin API\n    /**\n     * Creates a new user.\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async createUser(attributes) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/admin/users`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get a list of users.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n     */\n    async listUsers(params) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        try {\n            const pagination = { nextPage: null, lastPage: 0, total: 0 };\n            const response = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users`, {\n                headers: this.headers,\n                noResolveJson: true,\n                query: {\n                    page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n                    per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : '',\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._noResolveJsonResponse,\n            });\n            if (response.error)\n                throw response.error;\n            const users = await response.json();\n            const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n            const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n            if (links.length > 0) {\n                links.forEach((link) => {\n                    const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n                    const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n                    pagination[`${rel}Page`] = page;\n                });\n                pagination.total = parseInt(total);\n            }\n            return { data: Object.assign(Object.assign({}, users), pagination), error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { users: [] }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get user by id.\n     *\n     * @param uid The user's unique identifier\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async getUserById(uid) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates the user data.\n     *\n     * @param attributes The data you want to update.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async updateUserById(uid, attributes) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Delete a user. Requires a `service_role` key.\n     *\n     * @param id The user id you want to remove.\n     * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema. Soft deletion allows user identification from the hashed user ID but is not reversible.\n     * Defaults to false for backward compatibility.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async deleteUser(id, shouldSoftDelete = false) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n                headers: this.headers,\n                body: {\n                    should_soft_delete: shouldSoftDelete,\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _listFactors(params) {\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users/${params.userId}/factors`, {\n                headers: this.headers,\n                xform: (factors) => {\n                    return { data: { factors }, error: null };\n                },\n            });\n            return { data, error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _deleteFactor(params) {\n        try {\n            const data = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'DELETE', `${this.url}/admin/users/${params.userId}/factors/${params.id}`, {\n                headers: this.headers,\n            });\n            return { data, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\n//# sourceMappingURL=GoTrueAdminApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoTrueClient)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_local_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/local-storage */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js\");\n/* harmony import */ var _lib_polyfills__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/polyfills */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js\");\n/* harmony import */ var _lib_version__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/version */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/version.js\");\n/* harmony import */ var _lib_locks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/locks */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js\");\n/* harmony import */ var _lib_base64url__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/base64url */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\");\n\n\n\n\n\n\n\n\n\n\n(0,_lib_polyfills__WEBPACK_IMPORTED_MODULE_6__.polyfillGlobalThis)(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n    url: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.GOTRUE_URL,\n    storageKey: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEY,\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    headers: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_HEADERS,\n    flowType: 'implicit',\n    debug: false,\n    hasCustomAuthorizationHeader: false,\n};\nasync function lockNoOp(name, acquireTimeout, fn) {\n    return await fn();\n}\nclass GoTrueClient {\n    /**\n     * Create a new client for use in the browser.\n     */\n    constructor(options) {\n        var _a, _b;\n        this.memoryStorage = null;\n        this.stateChangeEmitters = new Map();\n        this.autoRefreshTicker = null;\n        this.visibilityChangedCallback = null;\n        this.refreshingDeferred = null;\n        /**\n         * Keeps track of the async client initialization.\n         * When null or not yet resolved the auth state is `unknown`\n         * Once resolved the the auth state is known and it's save to call any further client methods.\n         * Keep extra care to never reject or throw uncaught errors\n         */\n        this.initializePromise = null;\n        this.detectSessionInUrl = true;\n        this.hasCustomAuthorizationHeader = false;\n        this.suppressGetSessionWarning = false;\n        this.lockAcquired = false;\n        this.pendingInLock = [];\n        /**\n         * Used to broadcast state change events to other tabs listening.\n         */\n        this.broadcastChannel = null;\n        this.logger = console.log;\n        this.instanceID = GoTrueClient.nextInstanceID;\n        GoTrueClient.nextInstanceID += 1;\n        if (this.instanceID > 0 && (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)()) {\n            console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n        }\n        const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n        this.logDebugMessages = !!settings.debug;\n        if (typeof settings.debug === 'function') {\n            this.logger = settings.debug;\n        }\n        this.persistSession = settings.persistSession;\n        this.storageKey = settings.storageKey;\n        this.autoRefreshToken = settings.autoRefreshToken;\n        this.admin = new _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            url: settings.url,\n            headers: settings.headers,\n            fetch: settings.fetch,\n        });\n        this.url = settings.url;\n        this.headers = settings.headers;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.resolveFetch)(settings.fetch);\n        this.lock = settings.lock || lockNoOp;\n        this.detectSessionInUrl = settings.detectSessionInUrl;\n        this.flowType = settings.flowType;\n        this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader;\n        if (settings.lock) {\n            this.lock = settings.lock;\n        }\n        else if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {\n            this.lock = _lib_locks__WEBPACK_IMPORTED_MODULE_8__.navigatorLock;\n        }\n        else {\n            this.lock = lockNoOp;\n        }\n        this.jwks = { keys: [] };\n        this.jwks_cached_at = Number.MIN_SAFE_INTEGER;\n        this.mfa = {\n            verify: this._verify.bind(this),\n            enroll: this._enroll.bind(this),\n            unenroll: this._unenroll.bind(this),\n            challenge: this._challenge.bind(this),\n            listFactors: this._listFactors.bind(this),\n            challengeAndVerify: this._challengeAndVerify.bind(this),\n            getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n        };\n        if (this.persistSession) {\n            if (settings.storage) {\n                this.storage = settings.storage;\n            }\n            else {\n                if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.supportsLocalStorage)()) {\n                    this.storage = _lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.localStorageAdapter;\n                }\n                else {\n                    this.memoryStorage = {};\n                    this.storage = (0,_lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.memoryLocalStorageAdapter)(this.memoryStorage);\n                }\n            }\n        }\n        else {\n            this.memoryStorage = {};\n            this.storage = (0,_lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.memoryLocalStorageAdapter)(this.memoryStorage);\n        }\n        if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n            try {\n                this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n            }\n            catch (e) {\n                console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n            }\n            (_b = this.broadcastChannel) === null || _b === void 0 ? void 0 : _b.addEventListener('message', async (event) => {\n                this._debug('received broadcast notification from other tab or client', event);\n                await this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n            });\n        }\n        this.initialize();\n    }\n    _debug(...args) {\n        if (this.logDebugMessages) {\n            this.logger(`GoTrueClient@${this.instanceID} (${_lib_version__WEBPACK_IMPORTED_MODULE_7__.version}) ${new Date().toISOString()}`, ...args);\n        }\n        return this;\n    }\n    /**\n     * Initializes the client session either from the url or from storage.\n     * This method is automatically called when instantiating the client, but should also be called\n     * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n     */\n    async initialize() {\n        if (this.initializePromise) {\n            return await this.initializePromise;\n        }\n        this.initializePromise = (async () => {\n            return await this._acquireLock(-1, async () => {\n                return await this._initialize();\n            });\n        })();\n        return await this.initializePromise;\n    }\n    /**\n     * IMPORTANT:\n     * 1. Never throw in this method, as it is called from the constructor\n     * 2. Never return a session from this method as it would be cached over\n     *    the whole lifetime of the client\n     */\n    async _initialize() {\n        var _a;\n        try {\n            const params = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.parseParametersFromURL)(window.location.href);\n            let callbackUrlType = 'none';\n            if (this._isImplicitGrantCallback(params)) {\n                callbackUrlType = 'implicit';\n            }\n            else if (await this._isPKCECallback(params)) {\n                callbackUrlType = 'pkce';\n            }\n            /**\n             * Attempt to get the session from the URL only if these conditions are fulfilled\n             *\n             * Note: If the URL isn't one of the callback url types (implicit or pkce),\n             * then there could be an existing session so we don't want to prematurely remove it\n             */\n            if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n                const { data, error } = await this._getSessionFromURL(params, callbackUrlType);\n                if (error) {\n                    this._debug('#_initialize()', 'error detecting session from URL', error);\n                    if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthImplicitGrantRedirectError)(error)) {\n                        const errorCode = (_a = error.details) === null || _a === void 0 ? void 0 : _a.code;\n                        if (errorCode === 'identity_already_exists' ||\n                            errorCode === 'identity_not_found' ||\n                            errorCode === 'single_identity_not_deletable') {\n                            return { error };\n                        }\n                    }\n                    // failed login attempt via url,\n                    // remove old session as in verifyOtp, signUp and signInWith*\n                    await this._removeSession();\n                    return { error };\n                }\n                const { session, redirectType } = data;\n                this._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n                await this._saveSession(session);\n                setTimeout(async () => {\n                    if (redirectType === 'recovery') {\n                        await this._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n                    }\n                    else {\n                        await this._notifyAllSubscribers('SIGNED_IN', session);\n                    }\n                }, 0);\n                return { error: null };\n            }\n            // no login attempt via callback url try to recover session from storage\n            await this._recoverAndRefresh();\n            return { error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { error };\n            }\n            return {\n                error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthUnknownError('Unexpected error during initialization', error),\n            };\n        }\n        finally {\n            await this._handleVisibilityChange();\n            this._debug('#_initialize()', 'end');\n        }\n    }\n    /**\n     * Creates a new anonymous user.\n     *\n     * @returns A session where the is_anonymous claim in the access token JWT set to true\n     */\n    async signInAnonymously(credentials) {\n        var _a, _b, _c;\n        try {\n            const res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                headers: this.headers,\n                body: {\n                    data: (_b = (_a = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _a === void 0 ? void 0 : _a.data) !== null && _b !== void 0 ? _b : {},\n                    gotrue_meta_security: { captcha_token: (_c = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _c === void 0 ? void 0 : _c.captchaToken },\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Creates a new user.\n     *\n     * Be aware that if a user account exists in the system you may get back an\n     * error message that attempts to hide this information from the user.\n     * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n     *\n     * @returns A logged-in session if the server has \"autoconfirm\" ON\n     * @returns A user if the server has \"autoconfirm\" OFF\n     */\n    async signUp(credentials) {\n        var _a, _b, _c;\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: {\n                        email,\n                        password,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n                        channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }\n            else {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user with an email and password or phone and password.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or that the\n     * email/phone and password combination is wrong or that the account can only\n     * be accessed via social login.\n     */\n    async signInWithPassword(credentials) {\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponsePassword,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponsePassword,\n                });\n            }\n            else {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return { data: { user: null, session: null }, error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError() };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return {\n                data: Object.assign({ user: data.user, session: data.session }, (data.weak_password ? { weakPassword: data.weak_password } : null)),\n                error,\n            };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user via a third-party provider.\n     * This method supports the PKCE flow.\n     */\n    async signInWithOAuth(credentials) {\n        var _a, _b, _c, _d;\n        return await this._handleProviderSignIn(credentials.provider, {\n            redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n            scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n            queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n            skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect,\n        });\n    }\n    /**\n     * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n     */\n    async exchangeCodeForSession(authCode) {\n        await this.initializePromise;\n        return this._acquireLock(-1, async () => {\n            return this._exchangeCodeForSession(authCode);\n        });\n    }\n    async _exchangeCodeForSession(authCode) {\n        const storageItem = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n        const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=pkce`, {\n                headers: this.headers,\n                body: {\n                    auth_code: authCode,\n                    code_verifier: codeVerifier,\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n            if (error) {\n                throw error;\n            }\n            if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null, redirectType: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data: Object.assign(Object.assign({}, data), { redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null }), error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Allows signing in with an OIDC ID token. The authentication provider used\n     * should be enabled and configured.\n     */\n    async signInWithIdToken(credentials) {\n        try {\n            const { options, provider, token, access_token, nonce } = credentials;\n            const res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n                headers: this.headers,\n                body: {\n                    provider,\n                    id_token: token,\n                    access_token,\n                    nonce,\n                    gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data, error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user using magiclink or a one-time password (OTP).\n     *\n     * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n     * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n     * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or, that the account\n     * can only be accessed via social login.\n     *\n     * Do note that you will need to configure a Whatsapp sender on Twilio\n     * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n     * channel is not supported on other providers\n     * at this time.\n     * This method supports PKCE when an email is passed.\n     */\n    async signInWithOtp(credentials) {\n        var _a, _b, _c, _d, _e;\n        try {\n            if ('email' in credentials) {\n                const { email, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            if ('phone' in credentials) {\n                const { phone, options } = credentials;\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n                        create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms',\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number.');\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n     */\n    async verifyOtp(params) {\n        var _a, _b;\n        try {\n            let redirectTo = undefined;\n            let captchaToken = undefined;\n            if ('options' in params) {\n                redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n                captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n            }\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/verify`, {\n                headers: this.headers,\n                body: Object.assign(Object.assign({}, params), { gotrue_meta_security: { captcha_token: captchaToken } }),\n                redirectTo,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data) {\n                throw new Error('An error occurred on token verification.');\n            }\n            const session = data.session;\n            const user = data.user;\n            if (session === null || session === void 0 ? void 0 : session.access_token) {\n                await this._saveSession(session);\n                await this._notifyAllSubscribers(params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Attempts a single-sign on using an enterprise Identity Provider. A\n     * successful SSO attempt will redirect the current page to the identity\n     * provider authorization page. The redirect URL is implementation and SSO\n     * protocol specific.\n     *\n     * You can use it by providing a SSO domain. Typically you can extract this\n     * domain by asking users for their email address. If this domain is\n     * registered on the Auth instance the redirect will use that organization's\n     * currently active SSO Identity Provider for the login.\n     *\n     * If you have built an organization-specific login page, you can use the\n     * organization's SSO Identity Provider UUID directly instead.\n     */\n    async signInWithSSO(params) {\n        var _a, _b, _c;\n        try {\n            let codeChallenge = null;\n            let codeChallengeMethod = null;\n            if (this.flowType === 'pkce') {\n                ;\n                [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n            }\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/sso`, {\n                body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, ('providerId' in params ? { provider_id: params.providerId } : null)), ('domain' in params ? { domain: params.domain } : null)), { redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined }), (((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n                    : null)), { skip_http_redirect: true, code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._ssoResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends a reauthentication OTP to the user's email or phone number.\n     * Requires the user to be signed-in.\n     */\n    async reauthenticate() {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._reauthenticate();\n        });\n    }\n    async _reauthenticate() {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError)\n                    throw sessionError;\n                if (!session)\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n                    headers: this.headers,\n                    jwt: session.access_token,\n                });\n                return { data: { user: null, session: null }, error };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n     */\n    async resend(credentials) {\n        try {\n            const endpoint = `${this.url}/resend`;\n            if ('email' in credentials) {\n                const { email, type, options } = credentials;\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            else if ('phone' in credentials) {\n                const { phone, type, options } = credentials;\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns the session, refreshing it if necessary.\n     *\n     * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n     *\n     * **IMPORTANT:** This method loads values directly from the storage attached\n     * to the client. If that storage is based on request cookies for example,\n     * the values in it may not be authentic and therefore it's strongly advised\n     * against using this method and its results in such circumstances. A warning\n     * will be emitted if this is detected. Use {@link #getUser()} instead.\n     */\n    async getSession() {\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return this._useSession(async (result) => {\n                return result;\n            });\n        });\n        return result;\n    }\n    /**\n     * Acquires a global lock based on the storage key.\n     */\n    async _acquireLock(acquireTimeout, fn) {\n        this._debug('#_acquireLock', 'begin', acquireTimeout);\n        try {\n            if (this.lockAcquired) {\n                const last = this.pendingInLock.length\n                    ? this.pendingInLock[this.pendingInLock.length - 1]\n                    : Promise.resolve();\n                const result = (async () => {\n                    await last;\n                    return await fn();\n                })();\n                this.pendingInLock.push((async () => {\n                    try {\n                        await result;\n                    }\n                    catch (e) {\n                        // we just care if it finished\n                    }\n                })());\n                return result;\n            }\n            return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n                this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey);\n                try {\n                    this.lockAcquired = true;\n                    const result = fn();\n                    this.pendingInLock.push((async () => {\n                        try {\n                            await result;\n                        }\n                        catch (e) {\n                            // we just care if it finished\n                        }\n                    })());\n                    await result;\n                    // keep draining the queue until there's nothing to wait on\n                    while (this.pendingInLock.length) {\n                        const waitOn = [...this.pendingInLock];\n                        await Promise.all(waitOn);\n                        this.pendingInLock.splice(0, waitOn.length);\n                    }\n                    return await result;\n                }\n                finally {\n                    this._debug('#_acquireLock', 'lock released for storage key', this.storageKey);\n                    this.lockAcquired = false;\n                }\n            });\n        }\n        finally {\n            this._debug('#_acquireLock', 'end');\n        }\n    }\n    /**\n     * Use instead of {@link #getSession} inside the library. It is\n     * semantically usually what you want, as getting a session involves some\n     * processing afterwards that requires only one client operating on the\n     * session at once across multiple tabs or processes.\n     */\n    async _useSession(fn) {\n        this._debug('#_useSession', 'begin');\n        try {\n            // the use of __loadSession here is the only correct use of the function!\n            const result = await this.__loadSession();\n            return await fn(result);\n        }\n        finally {\n            this._debug('#_useSession', 'end');\n        }\n    }\n    /**\n     * NEVER USE DIRECTLY!\n     *\n     * Always use {@link #_useSession}.\n     */\n    async __loadSession() {\n        this._debug('#__loadSession()', 'begin');\n        if (!this.lockAcquired) {\n            this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n        }\n        try {\n            let currentSession = null;\n            const maybeSession = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, this.storageKey);\n            this._debug('#getSession()', 'session from storage', maybeSession);\n            if (maybeSession !== null) {\n                if (this._isValidSession(maybeSession)) {\n                    currentSession = maybeSession;\n                }\n                else {\n                    this._debug('#getSession()', 'session from storage is not valid');\n                    await this._removeSession();\n                }\n            }\n            if (!currentSession) {\n                return { data: { session: null }, error: null };\n            }\n            // A session is considered expired before the access token _actually_\n            // expires. When the autoRefreshToken option is off (or when the tab is\n            // in the background), very eager users of getSession() -- like\n            // realtime-js -- might send a valid JWT which will expire by the time it\n            // reaches the server.\n            const hasExpired = currentSession.expires_at\n                ? currentSession.expires_at * 1000 - Date.now() < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS\n                : false;\n            this._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n            if (!hasExpired) {\n                if (this.storage.isServer) {\n                    let suppressWarning = this.suppressGetSessionWarning;\n                    const proxySession = new Proxy(currentSession, {\n                        get: (target, prop, receiver) => {\n                            if (!suppressWarning && prop === 'user') {\n                                // only show warning when the user object is being accessed from the server\n                                console.warn('Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.');\n                                suppressWarning = true; // keeps this proxy instance from logging additional warnings\n                                this.suppressGetSessionWarning = true; // keeps this client's future proxy instances from warning\n                            }\n                            return Reflect.get(target, prop, receiver);\n                        },\n                    });\n                    currentSession = proxySession;\n                }\n                return { data: { session: currentSession }, error: null };\n            }\n            const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n            if (error) {\n                return { data: { session: null }, error };\n            }\n            return { data: { session }, error: null };\n        }\n        finally {\n            this._debug('#__loadSession()', 'end');\n        }\n    }\n    /**\n     * Gets the current user details if there is an existing session. This method\n     * performs a network request to the Supabase Auth server, so the returned\n     * value is authentic and can be used to base authorization rules on.\n     *\n     * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n     */\n    async getUser(jwt) {\n        if (jwt) {\n            return await this._getUser(jwt);\n        }\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return await this._getUser();\n        });\n        return result;\n    }\n    async _getUser(jwt) {\n        try {\n            if (jwt) {\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: jwt,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n            }\n            return await this._useSession(async (result) => {\n                var _a, _b, _c;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                // returns an error if there is no access_token or custom authorization header\n                if (!((_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) && !this.hasCustomAuthorizationHeader) {\n                    return { data: { user: null }, error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError() };\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: (_c = (_b = data.session) === null || _b === void 0 ? void 0 : _b.access_token) !== null && _c !== void 0 ? _c : undefined,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthSessionMissingError)(error)) {\n                    // JWT contains a `session_id` which does not correspond to an active\n                    // session in the database, indicating the user is signed out.\n                    await this._removeSession();\n                    await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n                }\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates user data for a logged in user.\n     */\n    async updateUser(attributes, options = {}) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._updateUser(attributes, options);\n        });\n    }\n    async _updateUser(attributes, options = {}) {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    throw sessionError;\n                }\n                if (!sessionData.session) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                }\n                const session = sessionData.session;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce' && attributes.email != null) {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                const { data, error: userError } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'PUT', `${this.url}/user`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: Object.assign(Object.assign({}, attributes), { code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                    jwt: session.access_token,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n                if (userError)\n                    throw userError;\n                session.user = data.user;\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('USER_UPDATED', session);\n                return { data: { user: session.user }, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n     * If the refresh token or access token in the current session is invalid, an error will be thrown.\n     * @param currentSession The current session that minimally contains an access token and refresh token.\n     */\n    async setSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._setSession(currentSession);\n        });\n    }\n    async _setSession(currentSession) {\n        try {\n            if (!currentSession.access_token || !currentSession.refresh_token) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n            }\n            const timeNow = Date.now() / 1000;\n            let expiresAt = timeNow;\n            let hasExpired = true;\n            let session = null;\n            const { payload } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(currentSession.access_token);\n            if (payload.exp) {\n                expiresAt = payload.exp;\n                hasExpired = expiresAt <= timeNow;\n            }\n            if (hasExpired) {\n                const { session: refreshedSession, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!refreshedSession) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                session = refreshedSession;\n            }\n            else {\n                const { data, error } = await this._getUser(currentSession.access_token);\n                if (error) {\n                    throw error;\n                }\n                session = {\n                    access_token: currentSession.access_token,\n                    refresh_token: currentSession.refresh_token,\n                    user: data.user,\n                    token_type: 'bearer',\n                    expires_in: expiresAt - timeNow,\n                    expires_at: expiresAt,\n                };\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user: session.user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns a new session, regardless of expiry status.\n     * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n     * If the current session's refresh token is invalid, an error will be thrown.\n     * @param currentSession The current session. If passed in, it must contain a refresh token.\n     */\n    async refreshSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._refreshSession(currentSession);\n        });\n    }\n    async _refreshSession(currentSession) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                if (!currentSession) {\n                    const { data, error } = result;\n                    if (error) {\n                        throw error;\n                    }\n                    currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n                }\n                if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                }\n                const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!session) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                return { data: { user: session.user, session }, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets the session data from a URL string\n     */\n    async _getSessionFromURL(params, callbackUrlType) {\n        try {\n            if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)())\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('No browser detected.');\n            // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n            if (params.error || params.error_description || params.error_code) {\n                // The error class returned implies that the redirect is from an implicit grant flow\n                // but it could also be from a redirect error from a PKCE flow.\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n                    error: params.error || 'unspecified_error',\n                    code: params.error_code || 'unspecified_code',\n                });\n            }\n            // Checks for mismatches between the flowType initialised in the client and the URL parameters\n            switch (callbackUrlType) {\n                case 'implicit':\n                    if (this.flowType === 'pkce') {\n                        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n                    }\n                    break;\n                case 'pkce':\n                    if (this.flowType === 'implicit') {\n                        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n                    }\n                    break;\n                default:\n                // there's no mismatch so we continue\n            }\n            // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n            if (callbackUrlType === 'pkce') {\n                this._debug('#_initialize()', 'begin', 'is PKCE flow', true);\n                if (!params.code)\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthPKCEGrantCodeExchangeError('No code detected.');\n                const { data, error } = await this._exchangeCodeForSession(params.code);\n                if (error)\n                    throw error;\n                const url = new URL(window.location.href);\n                url.searchParams.delete('code');\n                window.history.replaceState(window.history.state, '', url.toString());\n                return { data: { session: data.session, redirectType: null }, error: null };\n            }\n            const { provider_token, provider_refresh_token, access_token, refresh_token, expires_in, expires_at, token_type, } = params;\n            if (!access_token || !expires_in || !refresh_token || !token_type) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('No session defined in URL');\n            }\n            const timeNow = Math.round(Date.now() / 1000);\n            const expiresIn = parseInt(expires_in);\n            let expiresAt = timeNow + expiresIn;\n            if (expires_at) {\n                expiresAt = parseInt(expires_at);\n            }\n            const actuallyExpiresIn = expiresAt - timeNow;\n            if (actuallyExpiresIn * 1000 <= _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS) {\n                console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n            }\n            const issuedAt = expiresAt - expiresIn;\n            if (timeNow - issuedAt >= 120) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n            }\n            else if (timeNow - issuedAt < 0) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew', issuedAt, expiresAt, timeNow);\n            }\n            const { data, error } = await this._getUser(access_token);\n            if (error)\n                throw error;\n            const session = {\n                provider_token,\n                provider_refresh_token,\n                access_token,\n                expires_in: expiresIn,\n                expires_at: expiresAt,\n                refresh_token,\n                token_type,\n                user: data.user,\n            };\n            // Remove tokens from URL\n            window.location.hash = '';\n            this._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n            return { data: { session, redirectType: params.type }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n     */\n    _isImplicitGrantCallback(params) {\n        return Boolean(params.access_token || params.error_description);\n    }\n    /**\n     * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n     */\n    async _isPKCECallback(params) {\n        const currentStorageContent = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n        return !!(params.code && currentStorageContent);\n    }\n    /**\n     * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n     *\n     * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n     * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n     *\n     * If using `others` scope, no `SIGNED_OUT` event is fired!\n     */\n    async signOut(options = { scope: 'global' }) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._signOut(options);\n        });\n    }\n    async _signOut({ scope } = { scope: 'global' }) {\n        return await this._useSession(async (result) => {\n            var _a;\n            const { data, error: sessionError } = result;\n            if (sessionError) {\n                return { error: sessionError };\n            }\n            const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n            if (accessToken) {\n                const { error } = await this.admin.signOut(accessToken, scope);\n                if (error) {\n                    // ignore 404s since user might not exist anymore\n                    // ignore 401s since an invalid or expired JWT should sign out the current session\n                    if (!((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthApiError)(error) &&\n                        (error.status === 404 || error.status === 401 || error.status === 403))) {\n                        return { error };\n                    }\n                }\n            }\n            if (scope !== 'others') {\n                await this._removeSession();\n                await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n            }\n            return { error: null };\n        });\n    }\n    /**\n     * Receive a notification every time an auth event happens.\n     * @param callback A callback function to be invoked when an auth event happens.\n     */\n    onAuthStateChange(callback) {\n        const id = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.uuid)();\n        const subscription = {\n            id,\n            callback,\n            unsubscribe: () => {\n                this._debug('#unsubscribe()', 'state change callback with id removed', id);\n                this.stateChangeEmitters.delete(id);\n            },\n        };\n        this._debug('#onAuthStateChange()', 'registered callback with id', id);\n        this.stateChangeEmitters.set(id, subscription);\n        (async () => {\n            await this.initializePromise;\n            await this._acquireLock(-1, async () => {\n                this._emitInitialSession(id);\n            });\n        })();\n        return { data: { subscription } };\n    }\n    async _emitInitialSession(id) {\n        return await this._useSession(async (result) => {\n            var _a, _b;\n            try {\n                const { data: { session }, error, } = result;\n                if (error)\n                    throw error;\n                await ((_a = this.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n            }\n            catch (err) {\n                await ((_b = this.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n                console.error(err);\n            }\n        });\n    }\n    /**\n     * Sends a password reset request to an email address. This method supports the PKCE flow.\n     *\n     * @param email The email address of the user.\n     * @param options.redirectTo The URL to send the user to after they click the password reset link.\n     * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n     */\n    async resetPasswordForEmail(email, options = {}) {\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n            ;\n            [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey, true // isPasswordRecovery\n            );\n        }\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/recover`, {\n                body: {\n                    email,\n                    code_challenge: codeChallenge,\n                    code_challenge_method: codeChallengeMethod,\n                    gotrue_meta_security: { captcha_token: options.captchaToken },\n                },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets all the identities linked to a user.\n     */\n    async getUserIdentities() {\n        var _a;\n        try {\n            const { data, error } = await this.getUser();\n            if (error)\n                throw error;\n            return { data: { identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : [] }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Links an oauth identity to an existing user.\n     * This method supports the PKCE flow.\n     */\n    async linkIdentity(credentials) {\n        var _a;\n        try {\n            const { data, error } = await this._useSession(async (result) => {\n                var _a, _b, _c, _d, _e;\n                const { data, error } = result;\n                if (error)\n                    throw error;\n                const url = await this._getUrlForProvider(`${this.url}/user/identities/authorize`, credentials.provider, {\n                    redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n                    scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n                    queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n                    skipBrowserRedirect: true,\n                });\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', url, {\n                    headers: this.headers,\n                    jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined,\n                });\n            });\n            if (error)\n                throw error;\n            if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n                window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n            }\n            return { data: { provider: credentials.provider, url: data === null || data === void 0 ? void 0 : data.url }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { provider: credentials.provider, url: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n     */\n    async unlinkIdentity(identity) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'DELETE', `${this.url}/user/identities/${identity.identity_id}`, {\n                    headers: this.headers,\n                    jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates a new JWT.\n     * @param refreshToken A valid refresh token that was returned on login.\n     */\n    async _refreshAccessToken(refreshToken) {\n        const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            const startedAt = Date.now();\n            // will attempt to refresh the token with exponential backoff\n            return await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.retryable)(async (attempt) => {\n                if (attempt > 0) {\n                    await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.sleep)(200 * Math.pow(2, attempt - 1)); // 200, 400, 800, ...\n                }\n                this._debug(debugName, 'refreshing attempt', attempt);\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n                    body: { refresh_token: refreshToken },\n                    headers: this.headers,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }, (attempt, error) => {\n                const nextBackOffInterval = 200 * Math.pow(2, attempt);\n                return (error &&\n                    (0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error) &&\n                    // retryable only if the request can be sent before the backoff overflows the tick duration\n                    Date.now() + nextBackOffInterval - startedAt < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n            });\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    _isValidSession(maybeSession) {\n        const isValidSession = typeof maybeSession === 'object' &&\n            maybeSession !== null &&\n            'access_token' in maybeSession &&\n            'refresh_token' in maybeSession &&\n            'expires_at' in maybeSession;\n        return isValidSession;\n    }\n    async _handleProviderSignIn(provider, options) {\n        const url = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n            redirectTo: options.redirectTo,\n            scopes: options.scopes,\n            queryParams: options.queryParams,\n        });\n        this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n        // try to open on the browser\n        if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && !options.skipBrowserRedirect) {\n            window.location.assign(url);\n        }\n        return { data: { provider, url }, error: null };\n    }\n    /**\n     * Recovers the session from LocalStorage and refreshes the token\n     * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n     */\n    async _recoverAndRefresh() {\n        var _a;\n        const debugName = '#_recoverAndRefresh()';\n        this._debug(debugName, 'begin');\n        try {\n            const currentSession = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, this.storageKey);\n            this._debug(debugName, 'session from storage', currentSession);\n            if (!this._isValidSession(currentSession)) {\n                this._debug(debugName, 'session is not valid');\n                if (currentSession !== null) {\n                    await this._removeSession();\n                }\n                return;\n            }\n            const expiresWithMargin = ((_a = currentSession.expires_at) !== null && _a !== void 0 ? _a : Infinity) * 1000 - Date.now() < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS;\n            this._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS}s`);\n            if (expiresWithMargin) {\n                if (this.autoRefreshToken && currentSession.refresh_token) {\n                    const { error } = await this._callRefreshToken(currentSession.refresh_token);\n                    if (error) {\n                        console.error(error);\n                        if (!(0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error)) {\n                            this._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n                            await this._removeSession();\n                        }\n                    }\n                }\n            }\n            else {\n                // no need to persist currentSession again, as we just loaded it from\n                // local storage; persisting it again may overwrite a value saved by\n                // another client with access to the same local storage\n                await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n            }\n        }\n        catch (err) {\n            this._debug(debugName, 'error', err);\n            console.error(err);\n            return;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    async _callRefreshToken(refreshToken) {\n        var _a, _b;\n        if (!refreshToken) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n        }\n        // refreshing is already in progress\n        if (this.refreshingDeferred) {\n            return this.refreshingDeferred.promise;\n        }\n        const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            this.refreshingDeferred = new _lib_helpers__WEBPACK_IMPORTED_MODULE_4__.Deferred();\n            const { data, error } = await this._refreshAccessToken(refreshToken);\n            if (error)\n                throw error;\n            if (!data.session)\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n            await this._saveSession(data.session);\n            await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n            const result = { session: data.session, error: null };\n            this.refreshingDeferred.resolve(result);\n            return result;\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                const result = { session: null, error };\n                if (!(0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error)) {\n                    await this._removeSession();\n                }\n                (_a = this.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n                return result;\n            }\n            (_b = this.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n            throw error;\n        }\n        finally {\n            this.refreshingDeferred = null;\n            this._debug(debugName, 'end');\n        }\n    }\n    async _notifyAllSubscribers(event, session, broadcast = true) {\n        const debugName = `#_notifyAllSubscribers(${event})`;\n        this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n        try {\n            if (this.broadcastChannel && broadcast) {\n                this.broadcastChannel.postMessage({ event, session });\n            }\n            const errors = [];\n            const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n                try {\n                    await x.callback(event, session);\n                }\n                catch (e) {\n                    errors.push(e);\n                }\n            });\n            await Promise.all(promises);\n            if (errors.length > 0) {\n                for (let i = 0; i < errors.length; i += 1) {\n                    console.error(errors[i]);\n                }\n                throw errors[0];\n            }\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    /**\n     * set currentSession and currentUser\n     * process to _startAutoRefreshToken if possible\n     */\n    async _saveSession(session) {\n        this._debug('#_saveSession()', session);\n        // _saveSession is always called whenever a new session has been acquired\n        // so we can safely suppress the warning returned by future getSession calls\n        this.suppressGetSessionWarning = true;\n        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.setItemAsync)(this.storage, this.storageKey, session);\n    }\n    async _removeSession() {\n        this._debug('#_removeSession()');\n        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, this.storageKey);\n        await this._notifyAllSubscribers('SIGNED_OUT', null);\n    }\n    /**\n     * Removes any registered visibilitychange callback.\n     *\n     * {@see #startAutoRefresh}\n     * {@see #stopAutoRefresh}\n     */\n    _removeVisibilityChangedCallback() {\n        this._debug('#_removeVisibilityChangedCallback()');\n        const callback = this.visibilityChangedCallback;\n        this.visibilityChangedCallback = null;\n        try {\n            if (callback && (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n                window.removeEventListener('visibilitychange', callback);\n            }\n        }\n        catch (e) {\n            console.error('removing visibilitychange callback failed', e);\n        }\n    }\n    /**\n     * This is the private implementation of {@link #startAutoRefresh}. Use this\n     * within the library.\n     */\n    async _startAutoRefresh() {\n        await this._stopAutoRefresh();\n        this._debug('#_startAutoRefresh()');\n        const ticker = setInterval(() => this._autoRefreshTokenTick(), _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n        this.autoRefreshTicker = ticker;\n        if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n            // ticker is a NodeJS Timeout object that has an `unref` method\n            // https://nodejs.org/api/timers.html#timeoutunref\n            // When auto refresh is used in NodeJS (like for testing) the\n            // `setInterval` is preventing the process from being marked as\n            // finished and tests run endlessly. This can be prevented by calling\n            // `unref()` on the returned object.\n            ticker.unref();\n            // @ts-expect-error TS has no context of Deno\n        }\n        else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n            // similar like for NodeJS, but with the Deno API\n            // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n            // @ts-expect-error TS has no context of Deno\n            Deno.unrefTimer(ticker);\n        }\n        // run the tick immediately, but in the next pass of the event loop so that\n        // #_initialize can be allowed to complete without recursively waiting on\n        // itself\n        setTimeout(async () => {\n            await this.initializePromise;\n            await this._autoRefreshTokenTick();\n        }, 0);\n    }\n    /**\n     * This is the private implementation of {@link #stopAutoRefresh}. Use this\n     * within the library.\n     */\n    async _stopAutoRefresh() {\n        this._debug('#_stopAutoRefresh()');\n        const ticker = this.autoRefreshTicker;\n        this.autoRefreshTicker = null;\n        if (ticker) {\n            clearInterval(ticker);\n        }\n    }\n    /**\n     * Starts an auto-refresh process in the background. The session is checked\n     * every few seconds. Close to the time of expiration a process is started to\n     * refresh the session. If refreshing fails it will be retried for as long as\n     * necessary.\n     *\n     * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n     * to call this function, it will be called for you.\n     *\n     * On browsers the refresh process works only when the tab/window is in the\n     * foreground to conserve resources as well as prevent race conditions and\n     * flooding auth with requests. If you call this method any managed\n     * visibility change callback will be removed and you must manage visibility\n     * changes on your own.\n     *\n     * On non-browser platforms the refresh process works *continuously* in the\n     * background, which may not be desirable. You should hook into your\n     * platform's foreground indication mechanism and call these methods\n     * appropriately to conserve resources.\n     *\n     * {@see #stopAutoRefresh}\n     */\n    async startAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._startAutoRefresh();\n    }\n    /**\n     * Stops an active auto refresh process running in the background (if any).\n     *\n     * If you call this method any managed visibility change callback will be\n     * removed and you must manage visibility changes on your own.\n     *\n     * See {@link #startAutoRefresh} for more details.\n     */\n    async stopAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._stopAutoRefresh();\n    }\n    /**\n     * Runs the auto refresh token tick.\n     */\n    async _autoRefreshTokenTick() {\n        this._debug('#_autoRefreshTokenTick()', 'begin');\n        try {\n            await this._acquireLock(0, async () => {\n                try {\n                    const now = Date.now();\n                    try {\n                        return await this._useSession(async (result) => {\n                            const { data: { session }, } = result;\n                            if (!session || !session.refresh_token || !session.expires_at) {\n                                this._debug('#_autoRefreshTokenTick()', 'no session');\n                                return;\n                            }\n                            // session will expire in this many ticks (or has already expired if <= 0)\n                            const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n                            this._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n                            if (expiresInTicks <= _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_THRESHOLD) {\n                                await this._callRefreshToken(session.refresh_token);\n                            }\n                        });\n                    }\n                    catch (e) {\n                        console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n                    }\n                }\n                finally {\n                    this._debug('#_autoRefreshTokenTick()', 'end');\n                }\n            });\n        }\n        catch (e) {\n            if (e.isAcquireTimeout || e instanceof _lib_locks__WEBPACK_IMPORTED_MODULE_8__.LockAcquireTimeoutError) {\n                this._debug('auto refresh token tick lock not available');\n            }\n            else {\n                throw e;\n            }\n        }\n    }\n    /**\n     * Registers callbacks on the browser / platform, which in-turn run\n     * algorithms when the browser window/tab are in foreground. On non-browser\n     * platforms it assumes always foreground.\n     */\n    async _handleVisibilityChange() {\n        this._debug('#_handleVisibilityChange()');\n        if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n            if (this.autoRefreshToken) {\n                // in non-browser environments the refresh token ticker runs always\n                this.startAutoRefresh();\n            }\n            return false;\n        }\n        try {\n            this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false);\n            window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', this.visibilityChangedCallback);\n            // now immediately call the visbility changed callback to setup with the\n            // current visbility state\n            await this._onVisibilityChanged(true); // initial call\n        }\n        catch (error) {\n            console.error('_handleVisibilityChange', error);\n        }\n    }\n    /**\n     * Callback registered with `window.addEventListener('visibilitychange')`.\n     */\n    async _onVisibilityChanged(calledFromInitialize) {\n        const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n        this._debug(methodName, 'visibilityState', document.visibilityState);\n        if (document.visibilityState === 'visible') {\n            if (this.autoRefreshToken) {\n                // in browser environments the refresh token ticker runs only on focused tabs\n                // which prevents race conditions\n                this._startAutoRefresh();\n            }\n            if (!calledFromInitialize) {\n                // called when the visibility has changed, i.e. the browser\n                // transitioned from hidden -> visible so we need to see if the session\n                // should be recovered immediately... but to do that we need to acquire\n                // the lock first asynchronously\n                await this.initializePromise;\n                await this._acquireLock(-1, async () => {\n                    if (document.visibilityState !== 'visible') {\n                        this._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n                        // visibility has changed while waiting for the lock, abort\n                        return;\n                    }\n                    // recover the session\n                    await this._recoverAndRefresh();\n                });\n            }\n        }\n        else if (document.visibilityState === 'hidden') {\n            if (this.autoRefreshToken) {\n                this._stopAutoRefresh();\n            }\n        }\n    }\n    /**\n     * Generates the relevant login URL for a third-party provider.\n     * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n     * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n     * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n     */\n    async _getUrlForProvider(url, provider, options) {\n        const urlParams = [`provider=${encodeURIComponent(provider)}`];\n        if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n            urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n        }\n        if (options === null || options === void 0 ? void 0 : options.scopes) {\n            urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n        }\n        if (this.flowType === 'pkce') {\n            const [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n            const flowParams = new URLSearchParams({\n                code_challenge: `${encodeURIComponent(codeChallenge)}`,\n                code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n            });\n            urlParams.push(flowParams.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.queryParams) {\n            const query = new URLSearchParams(options.queryParams);\n            urlParams.push(query.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n            urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n        }\n        return `${url}?${urlParams.join('&')}`;\n    }\n    async _unenroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _enroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                const body = Object.assign({ friendly_name: params.friendlyName, factor_type: params.factorType }, (params.factorType === 'phone' ? { phone: params.phone } : { issuer: params.issuer }));\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors`, {\n                    body,\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n                if (error) {\n                    return { data: null, error };\n                }\n                if (params.factorType === 'totp' && ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code)) {\n                    data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n                }\n                return { data, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * {@see GoTrueMFAApi#verify}\n     */\n    async _verify(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/verify`, {\n                        body: { code: params.code, challenge_id: params.challengeId },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                    if (error) {\n                        return { data: null, error };\n                    }\n                    await this._saveSession(Object.assign({ expires_at: Math.round(Date.now() / 1000) + data.expires_in }, data));\n                    await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n                    return { data, error };\n                });\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challenge}\n     */\n    async _challenge(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/challenge`, {\n                        body: { channel: params.channel },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                });\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challengeAndVerify}\n     */\n    async _challengeAndVerify(params) {\n        // both _challenge and _verify independently acquire the lock, so no need\n        // to acquire it here\n        const { data: challengeData, error: challengeError } = await this._challenge({\n            factorId: params.factorId,\n        });\n        if (challengeError) {\n            return { data: null, error: challengeError };\n        }\n        return await this._verify({\n            factorId: params.factorId,\n            challengeId: challengeData.id,\n            code: params.code,\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#listFactors}\n     */\n    async _listFactors() {\n        // use #getUser instead of #_getUser as the former acquires a lock\n        const { data: { user }, error: userError, } = await this.getUser();\n        if (userError) {\n            return { data: null, error: userError };\n        }\n        const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n        const totp = factors.filter((factor) => factor.factor_type === 'totp' && factor.status === 'verified');\n        const phone = factors.filter((factor) => factor.factor_type === 'phone' && factor.status === 'verified');\n        return {\n            data: {\n                all: factors,\n                totp,\n                phone,\n            },\n            error: null,\n        };\n    }\n    /**\n     * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n     */\n    async _getAuthenticatorAssuranceLevel() {\n        return this._acquireLock(-1, async () => {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                if (!session) {\n                    return {\n                        data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n                        error: null,\n                    };\n                }\n                const { payload } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(session.access_token);\n                let currentLevel = null;\n                if (payload.aal) {\n                    currentLevel = payload.aal;\n                }\n                let nextLevel = currentLevel;\n                const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter((factor) => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n                if (verifiedFactors.length > 0) {\n                    nextLevel = 'aal2';\n                }\n                const currentAuthenticationMethods = payload.amr || [];\n                return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null };\n            });\n        });\n    }\n    async fetchJwk(kid, jwks = { keys: [] }) {\n        // try fetching from the supplied jwks\n        let jwk = jwks.keys.find((key) => key.kid === kid);\n        if (jwk) {\n            return jwk;\n        }\n        // try fetching from cache\n        jwk = this.jwks.keys.find((key) => key.kid === kid);\n        // jwk exists and jwks isn't stale\n        if (jwk && this.jwks_cached_at + _lib_constants__WEBPACK_IMPORTED_MODULE_1__.JWKS_TTL > Date.now()) {\n            return jwk;\n        }\n        // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n        const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n            headers: this.headers,\n        });\n        if (error) {\n            throw error;\n        }\n        if (!data.keys || data.keys.length === 0) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('JWKS is empty');\n        }\n        this.jwks = data;\n        this.jwks_cached_at = Date.now();\n        // Find the signing key\n        jwk = data.keys.find((key) => key.kid === kid);\n        if (!jwk) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('No matching signing key found in JWKS');\n        }\n        return jwk;\n    }\n    /**\n     * @experimental This method may change in future versions.\n     * @description Gets the claims from a JWT. If the JWT is symmetric JWTs, it will call getUser() to verify against the server. If the JWT is asymmetric, it will be verified against the JWKS using the WebCrypto API.\n     */\n    async getClaims(jwt, jwks = { keys: [] }) {\n        try {\n            let token = jwt;\n            if (!token) {\n                const { data, error } = await this.getSession();\n                if (error || !data.session) {\n                    return { data: null, error };\n                }\n                token = data.session.access_token;\n            }\n            const { header, payload, signature, raw: { header: rawHeader, payload: rawPayload }, } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(token);\n            // Reject expired JWTs\n            (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.validateExp)(payload.exp);\n            // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n            if (!header.kid ||\n                header.alg === 'HS256' ||\n                !('crypto' in globalThis && 'subtle' in globalThis.crypto)) {\n                const { error } = await this.getUser(token);\n                if (error) {\n                    throw error;\n                }\n                // getUser succeeds so the claims in the JWT can be trusted\n                return {\n                    data: {\n                        claims: payload,\n                        header,\n                        signature,\n                    },\n                    error: null,\n                };\n            }\n            const algorithm = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAlgorithm)(header.alg);\n            const signingKey = await this.fetchJwk(header.kid, jwks);\n            // Convert JWK to CryptoKey\n            const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, [\n                'verify',\n            ]);\n            // Verify the signature\n            const isValid = await crypto.subtle.verify(algorithm, publicKey, signature, (0,_lib_base64url__WEBPACK_IMPORTED_MODULE_9__.stringToUint8Array)(`${rawHeader}.${rawPayload}`));\n            if (!isValid) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('Invalid JWT signature');\n            }\n            // If verification succeeds, decode and return claims\n            return {\n                data: {\n                    claims: payload,\n                    header,\n                    signature,\n                },\n                error: null,\n            };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\nGoTrueClient.nextInstanceID = 0;\n//# sourceMappingURL=GoTrueClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _AuthAdminApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _AuthClient__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   AuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.CustomAuthError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _GoTrueClient__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.internals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.navigatorLock)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n/* harmony import */ var _GoTrueClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GoTrueClient */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\");\n/* harmony import */ var _AuthAdminApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthAdminApi */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js\");\n/* harmony import */ var _AuthClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthClient */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/AuthClient.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/types */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/types.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_locks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/locks */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js\");\n\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi42OS4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QztBQUNKO0FBQ0E7QUFDSjtBQUM0QjtBQUN0QztBQUNDO0FBQzhFO0FBQzNHIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHphY2hhXFxPbmVEcml2ZVxcRGVza3RvcFxcVW5pVmliZVxcVW5pVmliZS1wcm9qZWN0LXRyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrYXV0aC1qc0AyLjY5LjFcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxhdXRoLWpzXFxkaXN0XFxtb2R1bGVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBHb1RydWVBZG1pbkFwaSBmcm9tICcuL0dvVHJ1ZUFkbWluQXBpJztcbmltcG9ydCBHb1RydWVDbGllbnQgZnJvbSAnLi9Hb1RydWVDbGllbnQnO1xuaW1wb3J0IEF1dGhBZG1pbkFwaSBmcm9tICcuL0F1dGhBZG1pbkFwaSc7XG5pbXBvcnQgQXV0aENsaWVudCBmcm9tICcuL0F1dGhDbGllbnQnO1xuZXhwb3J0IHsgR29UcnVlQWRtaW5BcGksIEdvVHJ1ZUNsaWVudCwgQXV0aEFkbWluQXBpLCBBdXRoQ2xpZW50IH07XG5leHBvcnQgKiBmcm9tICcuL2xpYi90eXBlcyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9lcnJvcnMnO1xuZXhwb3J0IHsgbmF2aWdhdG9yTG9jaywgTmF2aWdhdG9yTG9ja0FjcXVpcmVUaW1lb3V0RXJyb3IsIGludGVybmFscyBhcyBsb2NrSW50ZXJuYWxzLCB9IGZyb20gJy4vbGliL2xvY2tzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64UrlToUint8Array: () => (/* binding */ base64UrlToUint8Array),\n/* harmony export */   byteFromBase64URL: () => (/* binding */ byteFromBase64URL),\n/* harmony export */   byteToBase64URL: () => (/* binding */ byteToBase64URL),\n/* harmony export */   codepointToUTF8: () => (/* binding */ codepointToUTF8),\n/* harmony export */   stringFromBase64URL: () => (/* binding */ stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* binding */ stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* binding */ stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* binding */ stringToUTF8),\n/* harmony export */   stringToUint8Array: () => (/* binding */ stringToUint8Array)\n/* harmony export */ });\n/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'.split('');\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = ' \\t\\n\\r='.split('');\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a byte to a Base64-URL string.\n *\n * @param byte The byte to convert, or null to flush at the end of the byte sequence.\n * @param state The Base64 conversion state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next Base64 character when ready.\n */\nfunction byteToBase64URL(byte, state, emit) {\n    if (byte !== null) {\n        state.queue = (state.queue << 8) | byte;\n        state.queuedBits += 8;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n    else if (state.queuedBits > 0) {\n        state.queue = state.queue << (6 - state.queuedBits);\n        state.queuedBits = 6;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n}\n/**\n * Converts a String char code (extracted using `string.charCodeAt(position)`) to a sequence of Base64-URL characters.\n *\n * @param charCode The char code of the JavaScript string.\n * @param state The Base64 state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next byte.\n */\nfunction byteFromBase64URL(charCode, state, emit) {\n    const bits = FROM_BASE64URL[charCode];\n    if (bits > -1) {\n        // valid Base64-URL character\n        state.queue = (state.queue << 6) | bits;\n        state.queuedBits += 6;\n        while (state.queuedBits >= 8) {\n            emit((state.queue >> (state.queuedBits - 8)) & 0xff);\n            state.queuedBits -= 8;\n        }\n    }\n    else if (bits === -2) {\n        // ignore spaces, tabs, newlines, =\n        return;\n    }\n    else {\n        throw new Error(`Invalid Base64-URL character \"${String.fromCharCode(charCode)}\"`);\n    }\n}\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nfunction stringToBase64URL(str) {\n    const base64 = [];\n    const emitter = (char) => {\n        base64.push(char);\n    };\n    const state = { queue: 0, queuedBits: 0 };\n    stringToUTF8(str, (byte) => {\n        byteToBase64URL(byte, state, emitter);\n    });\n    byteToBase64URL(null, state, emitter);\n    return base64.join('');\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nfunction stringFromBase64URL(str) {\n    const conv = [];\n    const utf8Emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const utf8State = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    const b64State = { queue: 0, queuedBits: 0 };\n    const byteEmit = (byte) => {\n        stringFromUTF8(byte, utf8State, utf8Emit);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), b64State, byteEmit);\n    }\n    return conv.join('');\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nfunction codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nfunction stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nfunction stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n/**\n * Helper functions to convert different types of strings to Uint8Array\n */\nfunction base64UrlToUint8Array(str) {\n    const result = [];\n    const state = { queue: 0, queuedBits: 0 };\n    const onByte = (byte) => {\n        result.push(byte);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), state, onByte);\n    }\n    return new Uint8Array(result);\n}\nfunction stringToUint8Array(str) {\n    const result = [];\n    stringToUTF8(str, (byte) => result.push(byte));\n    return new Uint8Array(result);\n}\n//# sourceMappingURL=base64url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_VERSIONS: () => (/* binding */ API_VERSIONS),\n/* harmony export */   API_VERSION_HEADER_NAME: () => (/* binding */ API_VERSION_HEADER_NAME),\n/* harmony export */   AUDIENCE: () => (/* binding */ AUDIENCE),\n/* harmony export */   AUTO_REFRESH_TICK_DURATION_MS: () => (/* binding */ AUTO_REFRESH_TICK_DURATION_MS),\n/* harmony export */   AUTO_REFRESH_TICK_THRESHOLD: () => (/* binding */ AUTO_REFRESH_TICK_THRESHOLD),\n/* harmony export */   BASE64URL_REGEX: () => (/* binding */ BASE64URL_REGEX),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   EXPIRY_MARGIN_MS: () => (/* binding */ EXPIRY_MARGIN_MS),\n/* harmony export */   GOTRUE_URL: () => (/* binding */ GOTRUE_URL),\n/* harmony export */   JWKS_TTL: () => (/* binding */ JWKS_TTL),\n/* harmony export */   NETWORK_FAILURE: () => (/* binding */ NETWORK_FAILURE),\n/* harmony export */   STORAGE_KEY: () => (/* binding */ STORAGE_KEY)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/version.js\");\n\n/** Current session will be checked for refresh at this interval. */\nconst AUTO_REFRESH_TICK_DURATION_MS = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nconst AUTO_REFRESH_TICK_THRESHOLD = 3;\n/*\n * Earliest time before an access token expires that the session should be refreshed.\n */\nconst EXPIRY_MARGIN_MS = AUTO_REFRESH_TICK_THRESHOLD * AUTO_REFRESH_TICK_DURATION_MS;\nconst GOTRUE_URL = 'http://localhost:9999';\nconst STORAGE_KEY = 'supabase.auth.token';\nconst AUDIENCE = '';\nconst DEFAULT_HEADERS = { 'X-Client-Info': `gotrue-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst NETWORK_FAILURE = {\n    MAX_RETRIES: 10,\n    RETRY_INTERVAL: 2, // in deciseconds\n};\nconst API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version';\nconst API_VERSIONS = {\n    '2024-01-01': {\n        timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n        name: '2024-01-01',\n    },\n};\nconst BASE64URL_REGEX = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;\nconst JWKS_TTL = 600000; // 10 minutes\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthApiError: () => (/* binding */ AuthApiError),\n/* harmony export */   AuthError: () => (/* binding */ AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* binding */ AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* binding */ AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* binding */ AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* binding */ AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* binding */ AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* binding */ AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* binding */ AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* binding */ AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* binding */ AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* binding */ CustomAuthError),\n/* harmony export */   isAuthApiError: () => (/* binding */ isAuthApiError),\n/* harmony export */   isAuthError: () => (/* binding */ isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* binding */ isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* binding */ isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* binding */ isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* binding */ isAuthWeakPasswordError)\n/* harmony export */ });\nclass AuthError extends Error {\n    constructor(message, status, code) {\n        super(message);\n        this.__isAuthError = true;\n        this.name = 'AuthError';\n        this.status = status;\n        this.code = code;\n    }\n}\nfunction isAuthError(error) {\n    return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nclass AuthApiError extends AuthError {\n    constructor(message, status, code) {\n        super(message, status, code);\n        this.name = 'AuthApiError';\n        this.status = status;\n        this.code = code;\n    }\n}\nfunction isAuthApiError(error) {\n    return isAuthError(error) && error.name === 'AuthApiError';\n}\nclass AuthUnknownError extends AuthError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'AuthUnknownError';\n        this.originalError = originalError;\n    }\n}\nclass CustomAuthError extends AuthError {\n    constructor(message, name, status, code) {\n        super(message, status, code);\n        this.name = name;\n        this.status = status;\n    }\n}\nclass AuthSessionMissingError extends CustomAuthError {\n    constructor() {\n        super('Auth session missing!', 'AuthSessionMissingError', 400, undefined);\n    }\n}\nfunction isAuthSessionMissingError(error) {\n    return isAuthError(error) && error.name === 'AuthSessionMissingError';\n}\nclass AuthInvalidTokenResponseError extends CustomAuthError {\n    constructor() {\n        super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined);\n    }\n}\nclass AuthInvalidCredentialsError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidCredentialsError', 400, undefined);\n    }\n}\nclass AuthImplicitGrantRedirectError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthImplicitGrantRedirectError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nfunction isAuthImplicitGrantRedirectError(error) {\n    return isAuthError(error) && error.name === 'AuthImplicitGrantRedirectError';\n}\nclass AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nclass AuthRetryableFetchError extends CustomAuthError {\n    constructor(message, status) {\n        super(message, 'AuthRetryableFetchError', status, undefined);\n    }\n}\nfunction isAuthRetryableFetchError(error) {\n    return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nclass AuthWeakPasswordError extends CustomAuthError {\n    constructor(message, status, reasons) {\n        super(message, 'AuthWeakPasswordError', status, 'weak_password');\n        this.reasons = reasons;\n    }\n}\nfunction isAuthWeakPasswordError(error) {\n    return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}\nclass AuthInvalidJwtError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidJwtError', 400, 'invalid_jwt');\n    }\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _generateLinkResponse: () => (/* binding */ _generateLinkResponse),\n/* harmony export */   _noResolveJsonResponse: () => (/* binding */ _noResolveJsonResponse),\n/* harmony export */   _request: () => (/* binding */ _request),\n/* harmony export */   _sessionResponse: () => (/* binding */ _sessionResponse),\n/* harmony export */   _sessionResponsePassword: () => (/* binding */ _sessionResponsePassword),\n/* harmony export */   _ssoResponse: () => (/* binding */ _ssoResponse),\n/* harmony export */   _userResponse: () => (/* binding */ _userResponse),\n/* harmony export */   handleError: () => (/* binding */ handleError)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nasync function handleError(error) {\n    var _a;\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_1__.looksLikeFetchResponse)(error)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(error), 0);\n    }\n    if (NETWORK_ERROR_CODES.includes(error.status)) {\n        // status in 500...599 range - server had an error, request might be retryed.\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(error), error.status);\n    }\n    let data;\n    try {\n        data = await error.json();\n    }\n    catch (e) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthUnknownError(_getErrorMessage(e), e);\n    }\n    let errorCode = undefined;\n    const responseAPIVersion = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.parseResponseAPIVersion)(error);\n    if (responseAPIVersion &&\n        responseAPIVersion.getTime() >= _constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSIONS['2024-01-01'].timestamp &&\n        typeof data === 'object' &&\n        data &&\n        typeof data.code === 'string') {\n        errorCode = data.code;\n    }\n    else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n        errorCode = data.error_code;\n    }\n    if (!errorCode) {\n        // Legacy support for weak password errors, when there were no error codes\n        if (typeof data === 'object' &&\n            data &&\n            typeof data.weak_password === 'object' &&\n            data.weak_password &&\n            Array.isArray(data.weak_password.reasons) &&\n            data.weak_password.reasons.length &&\n            data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n        }\n    }\n    else if (errorCode === 'weak_password') {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthWeakPasswordError(_getErrorMessage(data), error.status, ((_a = data.weak_password) === null || _a === void 0 ? void 0 : _a.reasons) || []);\n    }\n    else if (errorCode === 'session_not_found') {\n        // The `session_id` inside the JWT does not correspond to a row in the\n        // `sessions` table. This usually means the user has signed out, has been\n        // deleted, or their session has somehow been terminated.\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n    }\n    throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthApiError(_getErrorMessage(data), error.status || 500, errorCode);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET') {\n        return params;\n    }\n    params.headers = Object.assign({ 'Content-Type': 'application/json;charset=UTF-8' }, options === null || options === void 0 ? void 0 : options.headers);\n    params.body = JSON.stringify(body);\n    return Object.assign(Object.assign({}, params), parameters);\n};\nasync function _request(fetcher, method, url, options) {\n    var _a;\n    const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n    if (!headers[_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME]) {\n        headers[_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME] = _constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSIONS['2024-01-01'].name;\n    }\n    if (options === null || options === void 0 ? void 0 : options.jwt) {\n        headers['Authorization'] = `Bearer ${options.jwt}`;\n    }\n    const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n        qs['redirect_to'] = options.redirectTo;\n    }\n    const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n    const data = await _handleRequest(fetcher, method, url + queryString, {\n        headers,\n        noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson,\n    }, {}, options === null || options === void 0 ? void 0 : options.body);\n    return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : { data: Object.assign({}, data), error: null };\n}\nasync function _handleRequest(fetcher, method, url, options, parameters, body) {\n    const requestParams = _getRequestParams(method, options, parameters, body);\n    let result;\n    try {\n        result = await fetcher(url, Object.assign({}, requestParams));\n    }\n    catch (e) {\n        console.error(e);\n        // fetch failed, likely due to a network or CORS error\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(e), 0);\n    }\n    if (!result.ok) {\n        await handleError(result);\n    }\n    if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n        return result;\n    }\n    try {\n        return await result.json();\n    }\n    catch (e) {\n        await handleError(e);\n    }\n}\nfunction _sessionResponse(data) {\n    var _a;\n    let session = null;\n    if (hasSession(data)) {\n        session = Object.assign({}, data);\n        if (!data.expires_at) {\n            session.expires_at = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.expiresAt)(data.expires_in);\n        }\n    }\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { session, user }, error: null };\n}\nfunction _sessionResponsePassword(data) {\n    const response = _sessionResponse(data);\n    if (!response.error &&\n        data.weak_password &&\n        typeof data.weak_password === 'object' &&\n        Array.isArray(data.weak_password.reasons) &&\n        data.weak_password.reasons.length &&\n        data.weak_password.message &&\n        typeof data.weak_password.message === 'string' &&\n        data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n        response.data.weak_password = data.weak_password;\n    }\n    return response;\n}\nfunction _userResponse(data) {\n    var _a;\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { user }, error: null };\n}\nfunction _ssoResponse(data) {\n    return { data, error: null };\n}\nfunction _generateLinkResponse(data) {\n    const { action_link, email_otp, hashed_token, redirect_to, verification_type } = data, rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n    const properties = {\n        action_link,\n        email_otp,\n        hashed_token,\n        redirect_to,\n        verification_type,\n    };\n    const user = Object.assign({}, rest);\n    return {\n        data: {\n            properties,\n            user,\n        },\n        error: null,\n    };\n}\nfunction _noResolveJsonResponse(data) {\n    return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n    return data.access_token && data.refresh_token && data.expires_in;\n}\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Deferred: () => (/* binding */ Deferred),\n/* harmony export */   decodeJWT: () => (/* binding */ decodeJWT),\n/* harmony export */   expiresAt: () => (/* binding */ expiresAt),\n/* harmony export */   generatePKCEChallenge: () => (/* binding */ generatePKCEChallenge),\n/* harmony export */   generatePKCEVerifier: () => (/* binding */ generatePKCEVerifier),\n/* harmony export */   getAlgorithm: () => (/* binding */ getAlgorithm),\n/* harmony export */   getCodeChallengeAndMethod: () => (/* binding */ getCodeChallengeAndMethod),\n/* harmony export */   getItemAsync: () => (/* binding */ getItemAsync),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   looksLikeFetchResponse: () => (/* binding */ looksLikeFetchResponse),\n/* harmony export */   parseParametersFromURL: () => (/* binding */ parseParametersFromURL),\n/* harmony export */   parseResponseAPIVersion: () => (/* binding */ parseResponseAPIVersion),\n/* harmony export */   removeItemAsync: () => (/* binding */ removeItemAsync),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   retryable: () => (/* binding */ retryable),\n/* harmony export */   setItemAsync: () => (/* binding */ setItemAsync),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   supportsLocalStorage: () => (/* binding */ supportsLocalStorage),\n/* harmony export */   uuid: () => (/* binding */ uuid),\n/* harmony export */   validateExp: () => (/* binding */ validateExp)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _base64url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base64url */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\");\n\n\n\nfunction expiresAt(expiresIn) {\n    const timeNow = Math.round(Date.now() / 1000);\n    return timeNow + expiresIn;\n}\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nconst isBrowser = () => typeof window !== 'undefined' && typeof document !== 'undefined';\nconst localStorageWriteTests = {\n    tested: false,\n    writable: false,\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nconst supportsLocalStorage = () => {\n    if (!isBrowser()) {\n        return false;\n    }\n    try {\n        if (typeof globalThis.localStorage !== 'object') {\n            return false;\n        }\n    }\n    catch (e) {\n        // DOM exception when accessing `localStorage`\n        return false;\n    }\n    if (localStorageWriteTests.tested) {\n        return localStorageWriteTests.writable;\n    }\n    const randomKey = `lswt-${Math.random()}${Math.random()}`;\n    try {\n        globalThis.localStorage.setItem(randomKey, randomKey);\n        globalThis.localStorage.removeItem(randomKey);\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = true;\n    }\n    catch (e) {\n        // localStorage can't be written to\n        // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = false;\n    }\n    return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nfunction parseParametersFromURL(href) {\n    const result = {};\n    const url = new URL(href);\n    if (url.hash && url.hash[0] === '#') {\n        try {\n            const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n            hashSearchParams.forEach((value, key) => {\n                result[key] = value;\n            });\n        }\n        catch (e) {\n            // hash is not a query string\n        }\n    }\n    // search parameters take precedence over hash parameters\n    url.searchParams.forEach((value, key) => {\n        result[key] = value;\n    });\n    return result;\n}\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst looksLikeFetchResponse = (maybeResponse) => {\n    return (typeof maybeResponse === 'object' &&\n        maybeResponse !== null &&\n        'status' in maybeResponse &&\n        'ok' in maybeResponse &&\n        'json' in maybeResponse &&\n        typeof maybeResponse.json === 'function');\n};\n// Storage helpers\nconst setItemAsync = async (storage, key, data) => {\n    await storage.setItem(key, JSON.stringify(data));\n};\nconst getItemAsync = async (storage, key) => {\n    const value = await storage.getItem(key);\n    if (!value) {\n        return null;\n    }\n    try {\n        return JSON.parse(value);\n    }\n    catch (_a) {\n        return value;\n    }\n};\nconst removeItemAsync = async (storage, key) => {\n    await storage.removeItem(key);\n};\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nclass Deferred {\n    constructor() {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        this.promise = new Deferred.promiseConstructor((res, rej) => {\n            // eslint-disable-next-line @typescript-eslint/no-extra-semi\n            ;\n            this.resolve = res;\n            this.reject = rej;\n        });\n    }\n}\nDeferred.promiseConstructor = Promise;\nfunction decodeJWT(token) {\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.AuthInvalidJwtError('Invalid JWT structure');\n    }\n    // Regex checks for base64url format\n    for (let i = 0; i < parts.length; i++) {\n        if (!_constants__WEBPACK_IMPORTED_MODULE_0__.BASE64URL_REGEX.test(parts[i])) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_1__.AuthInvalidJwtError('JWT not in base64url format');\n        }\n    }\n    const data = {\n        // using base64url lib\n        header: JSON.parse((0,_base64url__WEBPACK_IMPORTED_MODULE_2__.stringFromBase64URL)(parts[0])),\n        payload: JSON.parse((0,_base64url__WEBPACK_IMPORTED_MODULE_2__.stringFromBase64URL)(parts[1])),\n        signature: (0,_base64url__WEBPACK_IMPORTED_MODULE_2__.base64UrlToUint8Array)(parts[2]),\n        raw: {\n            header: parts[0],\n            payload: parts[1],\n        },\n    };\n    return data;\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nasync function sleep(time) {\n    return await new Promise((accept) => {\n        setTimeout(() => accept(null), time);\n    });\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nfunction retryable(fn, isRetryable) {\n    const promise = new Promise((accept, reject) => {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        (async () => {\n            for (let attempt = 0; attempt < Infinity; attempt++) {\n                try {\n                    const result = await fn(attempt);\n                    if (!isRetryable(attempt, null, result)) {\n                        accept(result);\n                        return;\n                    }\n                }\n                catch (e) {\n                    if (!isRetryable(attempt, e)) {\n                        reject(e);\n                        return;\n                    }\n                }\n            }\n        })();\n    });\n    return promise;\n}\nfunction dec2hex(dec) {\n    return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nfunction generatePKCEVerifier() {\n    const verifierLength = 56;\n    const array = new Uint32Array(verifierLength);\n    if (typeof crypto === 'undefined') {\n        const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n        const charSetLen = charSet.length;\n        let verifier = '';\n        for (let i = 0; i < verifierLength; i++) {\n            verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n        }\n        return verifier;\n    }\n    crypto.getRandomValues(array);\n    return Array.from(array, dec2hex).join('');\n}\nasync function sha256(randomString) {\n    const encoder = new TextEncoder();\n    const encodedData = encoder.encode(randomString);\n    const hash = await crypto.subtle.digest('SHA-256', encodedData);\n    const bytes = new Uint8Array(hash);\n    return Array.from(bytes)\n        .map((c) => String.fromCharCode(c))\n        .join('');\n}\nasync function generatePKCEChallenge(verifier) {\n    const hasCryptoSupport = typeof crypto !== 'undefined' &&\n        typeof crypto.subtle !== 'undefined' &&\n        typeof TextEncoder !== 'undefined';\n    if (!hasCryptoSupport) {\n        console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n        return verifier;\n    }\n    const hashed = await sha256(verifier);\n    return btoa(hashed).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n}\nasync function getCodeChallengeAndMethod(storage, storageKey, isPasswordRecovery = false) {\n    const codeVerifier = generatePKCEVerifier();\n    let storedCodeVerifier = codeVerifier;\n    if (isPasswordRecovery) {\n        storedCodeVerifier += '/PASSWORD_RECOVERY';\n    }\n    await setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier);\n    const codeChallenge = await generatePKCEChallenge(codeVerifier);\n    const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n    return [codeChallenge, codeChallengeMethod];\n}\n/** Parses the API version which is 2YYY-MM-DD. */\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;\nfunction parseResponseAPIVersion(response) {\n    const apiVersion = response.headers.get(_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME);\n    if (!apiVersion) {\n        return null;\n    }\n    if (!apiVersion.match(API_VERSION_REGEX)) {\n        return null;\n    }\n    try {\n        const date = new Date(`${apiVersion}T00:00:00.0Z`);\n        return date;\n    }\n    catch (e) {\n        return null;\n    }\n}\nfunction validateExp(exp) {\n    if (!exp) {\n        throw new Error('Missing exp claim');\n    }\n    const timeNow = Math.floor(Date.now() / 1000);\n    if (exp <= timeNow) {\n        throw new Error('JWT has expired');\n    }\n}\nfunction getAlgorithm(alg) {\n    switch (alg) {\n        case 'RS256':\n            return {\n                name: 'RSASSA-PKCS1-v1_5',\n                hash: { name: 'SHA-256' },\n            };\n        case 'ES256':\n            return {\n                name: 'ECDSA',\n                namedCurve: 'P-256',\n                hash: { name: 'SHA-256' },\n            };\n        default:\n            throw new Error('Invalid alg claim');\n    }\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageAdapter: () => (/* binding */ localStorageAdapter),\n/* harmony export */   memoryLocalStorageAdapter: () => (/* binding */ memoryLocalStorageAdapter)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nconst localStorageAdapter = {\n    getItem: (key) => {\n        if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)()) {\n            return null;\n        }\n        return globalThis.localStorage.getItem(key);\n    },\n    setItem: (key, value) => {\n        if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)()) {\n            return;\n        }\n        globalThis.localStorage.setItem(key, value);\n    },\n    removeItem: (key) => {\n        if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)()) {\n            return;\n        }\n        globalThis.localStorage.removeItem(key);\n    },\n};\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nfunction memoryLocalStorageAdapter(store = {}) {\n    return {\n        getItem: (key) => {\n            return store[key] || null;\n        },\n        setItem: (key, value) => {\n            store[key] = value;\n        },\n        removeItem: (key) => {\n            delete store[key];\n        },\n    };\n}\n//# sourceMappingURL=local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LockAcquireTimeoutError: () => (/* binding */ LockAcquireTimeoutError),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* binding */ NavigatorLockAcquireTimeoutError),\n/* harmony export */   ProcessLockAcquireTimeoutError: () => (/* binding */ ProcessLockAcquireTimeoutError),\n/* harmony export */   internals: () => (/* binding */ internals),\n/* harmony export */   navigatorLock: () => (/* binding */ navigatorLock),\n/* harmony export */   processLock: () => (/* binding */ processLock)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n\n/**\n * @experimental\n */\nconst internals = {\n    /**\n     * @experimental\n     */\n    debug: !!(globalThis &&\n        (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)() &&\n        globalThis.localStorage &&\n        globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true'),\n};\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nclass LockAcquireTimeoutError extends Error {\n    constructor(message) {\n        super(message);\n        this.isAcquireTimeout = true;\n    }\n}\nclass NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\nclass ProcessLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with Safari being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nasync function navigatorLock(name, acquireTimeout, fn) {\n    if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n    }\n    const abortController = new globalThis.AbortController();\n    if (acquireTimeout > 0) {\n        setTimeout(() => {\n            abortController.abort();\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n            }\n        }, acquireTimeout);\n    }\n    // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n    // Wrapping navigator.locks.request() with a plain Promise is done as some\n    // libraries like zone.js patch the Promise object to track the execution\n    // context. However, it appears that most browsers use an internal promise\n    // implementation when using the navigator.locks.request() API causing them\n    // to lose context and emit confusing log messages or break certain features.\n    // This wrapping is believed to help zone.js track the execution context\n    // better.\n    return await Promise.resolve().then(() => globalThis.navigator.locks.request(name, acquireTimeout === 0\n        ? {\n            mode: 'exclusive',\n            ifAvailable: true,\n        }\n        : {\n            mode: 'exclusive',\n            signal: abortController.signal,\n        }, async (lock) => {\n        if (lock) {\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n            }\n            try {\n                return await fn();\n            }\n            finally {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n                }\n            }\n        }\n        else {\n            if (acquireTimeout === 0) {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n                }\n                throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n            }\n            else {\n                if (internals.debug) {\n                    try {\n                        const result = await globalThis.navigator.locks.query();\n                        console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n                    }\n                    catch (e) {\n                        console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n                    }\n                }\n                // Browser is not following the Navigator LockManager spec, it\n                // returned a null lock when we didn't use ifAvailable. So we can\n                // pretend the lock is acquired in the name of backward compatibility\n                // and user experience and just run the function.\n                console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n                return await fn();\n            }\n        }\n    }));\n}\nconst PROCESS_LOCKS = {};\n/**\n * Implements a global exclusive lock that works only in the current process.\n * Useful for environments like React Native or other non-browser\n * single-process (i.e. no concept of \"tabs\") environments.\n *\n * Use {@link #navigatorLock} in browser environments.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nasync function processLock(name, acquireTimeout, fn) {\n    var _a;\n    const previousOperation = (_a = PROCESS_LOCKS[name]) !== null && _a !== void 0 ? _a : Promise.resolve();\n    const currentOperation = Promise.race([\n        previousOperation.catch(() => {\n            // ignore error of previous operation that we're waiting to finish\n            return null;\n        }),\n        acquireTimeout >= 0\n            ? new Promise((_, reject) => {\n                setTimeout(() => {\n                    reject(new ProcessLockAcquireTimeoutError(`Acquring process lock with name \"${name}\" timed out`));\n                }, acquireTimeout);\n            })\n            : null,\n    ].filter((x) => x))\n        .catch((e) => {\n        if (e && e.isAcquireTimeout) {\n            throw e;\n        }\n        return null;\n    })\n        .then(async () => {\n        // previous operations finished and we didn't get a race on the acquire\n        // timeout, so the current operation can finally start\n        return await fn();\n    });\n    PROCESS_LOCKS[name] = currentOperation.catch(async (e) => {\n        if (e && e.isAcquireTimeout) {\n            // if the current operation timed out, it doesn't mean that the previous\n            // operation finished, so we need contnue waiting for it to finish\n            await previousOperation;\n            return null;\n        }\n        throw e;\n    });\n    // finally wait for the current operation to finish successfully, with an\n    // error or with an acquire timeout error\n    return await currentOperation;\n}\n//# sourceMappingURL=locks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polyfillGlobalThis: () => (/* binding */ polyfillGlobalThis)\n/* harmony export */ });\n/**\n * https://mathiasbynens.be/notes/globalthis\n */\nfunction polyfillGlobalThis() {\n    if (typeof globalThis === 'object')\n        return;\n    try {\n        Object.defineProperty(Object.prototype, '__magic__', {\n            get: function () {\n                return this;\n            },\n            configurable: true,\n        });\n        // @ts-expect-error 'Allow access to magic'\n        __magic__.globalThis = __magic__;\n        // @ts-expect-error 'Allow access to magic'\n        delete Object.prototype.__magic__;\n    }\n    catch (e) {\n        if (typeof self !== 'undefined') {\n            // @ts-expect-error 'Allow access to globals'\n            self.globalThis = self;\n        }\n    }\n}\n//# sourceMappingURL=polyfills.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi42OS4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9saWIvcG9seWZpbGxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx6YWNoYVxcT25lRHJpdmVcXERlc2t0b3BcXFVuaVZpYmVcXFVuaVZpYmUtcHJvamVjdC10clxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi42OS4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXHBvbHlmaWxscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGh0dHBzOi8vbWF0aGlhc2J5bmVucy5iZS9ub3Rlcy9nbG9iYWx0aGlzXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwb2x5ZmlsbEdsb2JhbFRoaXMoKSB7XG4gICAgaWYgKHR5cGVvZiBnbG9iYWxUaGlzID09PSAnb2JqZWN0JylcbiAgICAgICAgcmV0dXJuO1xuICAgIHRyeSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShPYmplY3QucHJvdG90eXBlLCAnX19tYWdpY19fJywge1xuICAgICAgICAgICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICB9KTtcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAnQWxsb3cgYWNjZXNzIHRvIG1hZ2ljJ1xuICAgICAgICBfX21hZ2ljX18uZ2xvYmFsVGhpcyA9IF9fbWFnaWNfXztcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAnQWxsb3cgYWNjZXNzIHRvIG1hZ2ljJ1xuICAgICAgICBkZWxldGUgT2JqZWN0LnByb3RvdHlwZS5fX21hZ2ljX187XG4gICAgfVxuICAgIGNhdGNoIChlKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygc2VsZiAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgJ0FsbG93IGFjY2VzcyB0byBnbG9iYWxzJ1xuICAgICAgICAgICAgc2VsZi5nbG9iYWxUaGlzID0gc2VsZjtcbiAgICAgICAgfVxuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBvbHlmaWxscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/types.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/types.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi42OS4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9saWIvdHlwZXMuanMiLCJtYXBwaW5ncyI6IjtBQUFVO0FBQ1YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZSthdXRoLWpzQDIuNjkuMVxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGF1dGgtanNcXGRpc3RcXG1vZHVsZVxcbGliXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/version.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/version.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.69.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi42OS4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9saWIvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx6YWNoYVxcT25lRHJpdmVcXERlc2t0b3BcXFVuaVZpYmVcXFVuaVZpYmUtcHJvamVjdC10clxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi42OS4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMi42OS4xJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.69.1/node_modules/@supabase/auth-js/dist/module/lib/version.js\n");

/***/ })

};
;