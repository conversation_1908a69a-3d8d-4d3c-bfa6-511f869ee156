import { supabase } from './supabase'
import type { Database } from './supabase'

type Event = Database['public']['Tables']['events']['Row']
type EventInsert = Database['public']['Tables']['events']['Insert']
type EventUpdate = Database['public']['Tables']['events']['Update']

export interface CreateEventData {
  title: string
  description: string
  date: string
  time: string
  location: string
  category: string
  organizer: string
  organizer_id?: string
  image_url?: string
  is_draft?: boolean
}

export interface EventFormData {
  title: string
  description: string
  date: string
  time: string
  venue: string
  category: string
  image: File | null
}

// Create a new event
export async function createEvent(eventData: CreateEventData): Promise<{ data: Event | null; error: string | null }> {
  try {
    console.log('Creating event with data:', eventData)

    const insertData = {
      title: eventData.title,
      description: eventData.description,
      date: eventData.date,
      time: eventData.time,
      location: eventData.location,
      category: eventData.category,
      organizer: eventData.organizer,
      organizer_id: eventData.organizer_id,
      image_url: eventData.image_url,
      attendees_count: 0
    }

    console.log('Insert data:', insertData)

    const { data, error } = await supabase
      .from('events')
      .insert(insertData)
      .select()
      .single()

    if (error) {
      console.error('Error creating event:', error)
      return { data: null, error: error.message || 'Unknown database error' }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error creating event:', error)
    return { data: null, error: 'An unexpected error occurred' }
  }
}

// Save event as draft (using a separate drafts table or a flag)
export async function saveDraft(eventData: CreateEventData & { id?: string; created_at?: string; updated_at?: string }): Promise<{ data: any | null; error: string | null }> {
  try {
    // For now, we'll save drafts in localStorage
    // In a real app, you might want a separate drafts table or a is_draft flag
    const drafts = getDrafts()

    // Use existing ID if provided (for updates), otherwise create new one
    const draftId = eventData.id || `draft_${Date.now()}`
    const existingDraft = drafts[draftId]

    const draftToSave = {
      id: draftId,
      ...eventData,
      created_at: eventData.created_at || existingDraft?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    drafts[draftId] = draftToSave
    localStorage.setItem('event_drafts', JSON.stringify(drafts))

    return { data: draftToSave, error: null }
  } catch (error) {
    console.error('Error saving draft:', error)
    return { data: null, error: 'Failed to save draft' }
  }
}

// Get all drafts
export function getDrafts(): Record<string, any> {
  try {
    const drafts = localStorage.getItem('event_drafts')
    const parsedDrafts = drafts ? JSON.parse(drafts) : {}

    // Clean up any potential duplicates or invalid entries
    const cleanedDrafts: Record<string, any> = {}
    Object.entries(parsedDrafts).forEach(([key, draft]: [string, any]) => {
      if (draft && draft.id && key === draft.id) {
        cleanedDrafts[key] = draft
      }
    })

    // Save cleaned drafts back if there were any changes
    if (Object.keys(cleanedDrafts).length !== Object.keys(parsedDrafts).length) {
      localStorage.setItem('event_drafts', JSON.stringify(cleanedDrafts))
    }

    return cleanedDrafts
  } catch (error) {
    console.error('Error getting drafts:', error)
    return {}
  }
}

// Delete a draft
export function deleteDraft(draftId: string): boolean {
  try {
    const drafts = getDrafts()
    delete drafts[draftId]
    localStorage.setItem('event_drafts', JSON.stringify(drafts))
    return true
  } catch (error) {
    console.error('Error deleting draft:', error)
    return false
  }
}

// Clean up drafts storage (utility function for debugging)
export function cleanupDrafts(): void {
  try {
    const drafts = getDrafts()
    const cleanedDrafts: Record<string, any> = {}

    // Only keep valid drafts with proper structure
    Object.entries(drafts).forEach(([key, draft]: [string, any]) => {
      if (draft &&
          draft.id &&
          key === draft.id &&
          typeof draft.title === 'string' &&
          typeof draft.created_at === 'string') {
        cleanedDrafts[key] = draft
      }
    })

    localStorage.setItem('event_drafts', JSON.stringify(cleanedDrafts))
    console.log('Drafts cleaned up. Remaining drafts:', Object.keys(cleanedDrafts).length)
  } catch (error) {
    console.error('Error cleaning up drafts:', error)
  }
}

// Publish a draft as a live event
export async function publishDraft(draftId: string): Promise<{ data: any | null; error: string | null }> {
  try {
    const drafts = getDrafts()
    const draft = drafts[draftId]

    if (!draft) {
      return { data: null, error: 'Draft not found' }
    }

    // Validate required fields
    if (!draft.title || !draft.date || !draft.time || !draft.location || !draft.category || !draft.organizer) {
      return { data: null, error: 'Draft is missing required fields. Please edit the draft and ensure all fields are filled.' }
    }

    // Create the event from the draft
    const { data, error } = await createEvent({
      title: draft.title,
      description: draft.description || '',
      date: draft.date,
      time: draft.time,
      location: draft.location,
      category: draft.category,
      organizer: draft.organizer,
      organizer_id: draft.organizer_id || null,
      image_url: draft.image_url || null,
    })

    if (error) {
      return { data: null, error }
    }

    // Delete the draft after successful publication
    deleteDraft(draftId)

    return { data, error: null }
  } catch (error) {
    console.error('Error publishing draft:', error)
    return { data: null, error: 'Failed to publish draft' }
  }
}

// Upload image to Supabase Storage
export async function uploadEventImage(file: File, eventId: string): Promise<{ url: string | null; error: string | null }> {
  try {
    console.log('Uploading image:', { fileName: file.name, fileSize: file.size, eventId })

    // Validate file
    if (!file) {
      return { url: null, error: 'No file provided' }
    }

    // Check file size (50MB limit)
    if (file.size > 50 * 1024 * 1024) {
      return { url: null, error: 'File size too large. Maximum size is 50MB.' }
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
      return { url: null, error: 'Invalid file type. Please upload a JPEG, PNG, WebP, or GIF image.' }
    }

    const fileExt = file.name.split('.').pop()
    const fileName = `${eventId}_${Date.now()}.${fileExt}`
    const filePath = `event-images/${fileName}`

    console.log('Uploading to path:', filePath)

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('events')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      })

    if (uploadError) {
      console.error('Error uploading image:', uploadError)
      return { url: null, error: uploadError.message || 'Failed to upload image to storage' }
    }

    console.log('Upload successful:', uploadData)

    const { data } = supabase.storage
      .from('events')
      .getPublicUrl(filePath)

    console.log('Public URL generated:', data.publicUrl)

    return { url: data.publicUrl, error: null }
  } catch (error) {
    console.error('Unexpected error uploading image:', error)
    return { url: null, error: 'Failed to upload image' }
  }
}

// Get all events
export async function getEvents(): Promise<{ data: Event[] | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .order('date', { ascending: true })

    if (error) {
      console.error('Error fetching events:', error)
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error fetching events:', error)
    return { data: null, error: 'Failed to fetch events' }
  }
}

// Get event by ID
export async function getEventById(id: string): Promise<{ data: Event | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching event:', error)
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error fetching event:', error)
    return { data: null, error: 'Failed to fetch event' }
  }
}

// Update event
export async function updateEvent(id: string, updates: EventUpdate): Promise<{ data: Event | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from('events')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating event:', error)
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error updating event:', error)
    return { data: null, error: 'Failed to update event' }
  }
}

// Delete event
export async function deleteEvent(id: string): Promise<{ error: string | null }> {
  try {
    const { error } = await supabase
      .from('events')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting event:', error)
      return { error: error.message }
    }

    return { error: null }
  } catch (error) {
    console.error('Unexpected error deleting event:', error)
    return { error: 'Failed to delete event' }
  }
}

// Validate event form data
export function validateEventForm(formData: EventFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!formData.title.trim()) {
    errors.push('Event title is required')
  }

  if (!formData.description.trim()) {
    errors.push('Event description is required')
  }

  if (!formData.date) {
    errors.push('Event date is required')
  } else {
    const eventDate = new Date(formData.date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    if (eventDate < today) {
      errors.push('Event date cannot be in the past')
    }
  }

  if (!formData.time) {
    errors.push('Event time is required')
  }

  if (!formData.venue.trim()) {
    errors.push('Event venue is required')
  }

  if (!formData.category) {
    errors.push('Event category is required')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
