"use client"

import { AuthGuard } from "@/components/auth-guard"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Bell, Calendar, Users, Star } from "lucide-react"
import Link from "next/link"

// Mock notifications data
const notifications = [
  {
    id: "1",
    type: "new_event",
    title: "New event posted: Club Jam Night!",
    message: "Music Society just posted a new event you might be interested in.",
    time: "2 hours ago",
    isRead: false,
    eventId: "1",
    icon: Bell,
  },
  {
    id: "2",
    type: "reminder",
    title: "Event starts in 1 hour",
    message: "Spring Music Festival 2024 starts at 7:00 PM today.",
    time: "1 hour ago",
    isRead: false,
    eventId: "1",
    icon: Calendar,
  },
  {
    id: "3",
    type: "update",
    title: "Event update",
    message: "Tech Talk: AI in Healthcare has been moved to Room 102.",
    time: "3 hours ago",
    isRead: true,
    eventId: "2",
    icon: Star,
  },
  {
    id: "4",
    type: "social",
    title: "50 new people joined your event",
    message: "Basketball Championship Finals is getting popular!",
    time: "1 day ago",
    isRead: true,
    eventId: "3",
    icon: Users,
  },
  {
    id: "5",
    type: "new_event",
    title: "New event in your favorite category",
    message: "Art Gallery Opening Night - Arts category event posted.",
    time: "2 days ago",
    isRead: true,
    eventId: "4",
    icon: Bell,
  },
]

export default function NotificationsPage() {
  const unreadCount = notifications.filter((n) => !n.isRead).length

  return (
    <AuthGuard requireAuth={true}>
      <div className="p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Recent Activity</h2>
        {unreadCount > 0 && (
          <Badge variant="secondary" className="bg-accent text-accent-foreground">
            {unreadCount} new
          </Badge>
        )}
      </div>

      {/* Notifications List */}
      <div className="space-y-3">
        {notifications.map((notification) => {
          const IconComponent = notification.icon

          return (
            <Link key={notification.id} href={notification.eventId ? `/event/${notification.eventId}` : "#"}>
              <Card
                className={`transition-all hover:shadow-md ${
                  !notification.isRead ? "border-primary/50 bg-primary/5" : ""
                }`}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div
                      className={`p-2 rounded-full ${
                        !notification.isRead ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                      }`}
                    >
                      <IconComponent className="h-4 w-4" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3
                          className={`font-medium text-sm ${
                            !notification.isRead ? "text-foreground" : "text-muted-foreground"
                          }`}
                        >
                          {notification.title}
                        </h3>
                        {!notification.isRead && <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />}
                      </div>

                      <p className="text-sm text-muted-foreground mb-2">{notification.message}</p>

                      <p className="text-xs text-muted-foreground">{notification.time}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          )
        })}
      </div>

      {notifications.length === 0 && (
        <div className="text-center py-12">
          <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No notifications yet</p>
          <p className="text-sm text-muted-foreground">We'll notify you about new events and updates</p>
        </div>
      )}
      </div>
    </AuthGuard>
  )
}
